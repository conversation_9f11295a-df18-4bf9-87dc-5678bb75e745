import { create } from 'zustand';

export interface BrandColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
}

export interface BrandFonts {
  primary: string;
  secondary: string;
}

export interface GraphicAssets {
  useGradients: boolean;
  useGlowEffects: boolean;
  useThickBorders: boolean;
  useBackgroundTexture: boolean;
  useGlassmorphism: boolean;
}

export interface ProductInfo {
  name: string;
  price: string;
  variants: string[];
  description: string;
}

export interface BusinessInput {
  name: string;
  description: string;
  visualStyle: string;
}

export interface WebSocketStatus {
  connected: boolean;
  lastFetched: Date | null;
  status: 'idle' | 'connecting' | 'generating' | 'completed' | 'error';
}

export interface BrandingState {
  // Input data
  businessInput: BusinessInput;
  
  // AI Generated data
  brandColors: BrandColors;
  brandTone: string;
  brandFonts: BrandFonts;
  graphicAssets: GraphicAssets;
  productInfo: ProductInfo;
  
  // WebSocket status
  websocketStatus: WebSocketStatus;
  
  // Actions
  updateBusinessInput: (input: Partial<BusinessInput>) => void;
  updateBrandColors: (colors: Partial<BrandColors>) => void;
  updateBrandTone: (tone: string) => void;
  updateBrandFonts: (fonts: Partial<BrandFonts>) => void;
  updateGraphicAssets: (assets: Partial<GraphicAssets>) => void;
  updateProductInfo: (info: Partial<ProductInfo>) => void;
  updateWebSocketStatus: (status: Partial<WebSocketStatus>) => void;
  resetBrandingData: () => void;
}

const initialState = {
  businessInput: {
    name: '',
    description: '',
    visualStyle: '',
  },
  brandColors: {
    primary: '#3B82F6',
    secondary: '#8B5CF6',
    accent: '#F59E0B',
    background: '#FFFFFF',
    text: '#1F2937',
  },
  brandTone: 'Professional and Modern',
  brandFonts: {
    primary: 'Inter',
    secondary: 'Roboto',
  },
  graphicAssets: {
    useGradients: true,
    useGlowEffects: false,
    useThickBorders: false,
    useBackgroundTexture: false,
    useGlassmorphism: true,
  },
  productInfo: {
    name: 'Premium Product',
    price: '$99.99',
    variants: ['Standard', 'Premium', 'Enterprise'],
    description: 'A high-quality product designed for modern businesses',
  },
  websocketStatus: {
    connected: false,
    lastFetched: null,
    status: 'idle' as const,
  },
};

export const useBrandingStore = create<BrandingState>((set) => ({
  ...initialState,
  
  updateBusinessInput: (input) =>
    set((state) => ({
      businessInput: { ...state.businessInput, ...input },
    })),
    
  updateBrandColors: (colors) =>
    set((state) => ({
      brandColors: { ...state.brandColors, ...colors },
    })),
    
  updateBrandTone: (tone) => set({ brandTone: tone }),
  
  updateBrandFonts: (fonts) =>
    set((state) => ({
      brandFonts: { ...state.brandFonts, ...fonts },
    })),
    
  updateGraphicAssets: (assets) =>
    set((state) => ({
      graphicAssets: { ...state.graphicAssets, ...assets },
    })),
    
  updateProductInfo: (info) =>
    set((state) => ({
      productInfo: { ...state.productInfo, ...info },
    })),
    
  updateWebSocketStatus: (status) =>
    set((state) => ({
      websocketStatus: { ...state.websocketStatus, ...status },
    })),
    
  resetBrandingData: () => set(initialState),
}));
