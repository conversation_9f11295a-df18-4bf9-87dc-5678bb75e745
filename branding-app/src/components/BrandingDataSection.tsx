'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useBrandingStore } from '@/store/brandingStore';
import { Palette, Type, Sparkles, Package, Edit3, Check, X } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function BrandingDataSection() {
  const {
    brandColors,
    brandTone,
    brandFonts,
    graphicAssets,
    productInfo,
    updateBrandColors,
    updateBrandTone,
    updateBrandFonts,
    updateGraphicAssets,
    updateProductInfo,
    websocketStatus
  } = useBrandingStore();

  const [editingField, setEditingField] = useState<string | null>(null);
  const [tempValue, setTempValue] = useState<string>('');

  const handleEdit = (field: string, currentValue: string) => {
    setEditingField(field);
    setTempValue(currentValue);
  };

  const handleSave = (field: string) => {
    switch (field) {
      case 'brandTone':
        updateBrandTone(tempValue);
        break;
      case 'primaryFont':
        updateBrandFonts({ primary: tempValue });
        break;
      case 'secondaryFont':
        updateBrandFonts({ secondary: tempValue });
        break;
      case 'productName':
        updateProductInfo({ name: tempValue });
        break;
      case 'productPrice':
        updateProductInfo({ price: tempValue });
        break;
      case 'productDescription':
        updateProductInfo({ description: tempValue });
        break;
    }
    setEditingField(null);
    setTempValue('');
  };

  const handleCancel = () => {
    setEditingField(null);
    setTempValue('');
  };

  const EditableField = ({ 
    field, 
    value, 
    label, 
    icon: Icon 
  }: { 
    field: string; 
    value: string; 
    label: string; 
    icon: any;
  }) => (
    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
      <div className="flex items-center space-x-2">
        <Icon className="h-4 w-4 text-gray-500" />
        <span className="text-sm font-medium text-gray-700">{label}:</span>
      </div>
      <div className="flex items-center space-x-2">
        {editingField === field ? (
          <>
            <Input
              value={tempValue}
              onChange={(e) => setTempValue(e.target.value)}
              className="h-8 w-32 text-sm"
              autoFocus
            />
            <Button size="sm" variant="ghost" onClick={() => handleSave(field)}>
              <Check className="h-3 w-3" />
            </Button>
            <Button size="sm" variant="ghost" onClick={handleCancel}>
              <X className="h-3 w-3" />
            </Button>
          </>
        ) : (
          <>
            <span className="text-sm text-gray-900">{value}</span>
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={() => handleEdit(field, value)}
              className="h-6 w-6 p-0"
            >
              <Edit3 className="h-3 w-3" />
            </Button>
          </>
        )}
      </div>
    </div>
  );

  return (
    <div className="p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        
        {/* Brand Colors */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center space-x-2">
              <Palette className="h-5 w-5" />
              <span>Brand Colors</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {Object.entries(brandColors).map(([key, color]) => (
              <div key={key} className="flex items-center justify-between">
                <span className="text-sm capitalize text-gray-600">{key}</span>
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-6 h-6 rounded border border-gray-200"
                    style={{ backgroundColor: color }}
                  />
                  <Input
                    value={color}
                    onChange={(e) => updateBrandColors({ [key]: e.target.value })}
                    className="h-8 w-20 text-xs font-mono"
                  />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Brand Tone & Fonts */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center space-x-2">
              <Type className="h-5 w-5" />
              <span>Typography</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <EditableField
              field="brandTone"
              value={brandTone}
              label="Brand Tone"
              icon={Sparkles}
            />
            <EditableField
              field="primaryFont"
              value={brandFonts.primary}
              label="Primary Font"
              icon={Type}
            />
            <EditableField
              field="secondaryFont"
              value={brandFonts.secondary}
              label="Secondary Font"
              icon={Type}
            />
          </CardContent>
        </Card>

        {/* Graphic Assets */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center space-x-2">
              <Sparkles className="h-5 w-5" />
              <span>Visual Effects</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {Object.entries(graphicAssets).map(([key, enabled]) => (
              <div key={key} className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </span>
                <Badge 
                  variant={enabled ? "default" : "secondary"}
                  className="cursor-pointer"
                  onClick={() => updateGraphicAssets({ [key]: !enabled })}
                >
                  {enabled ? 'On' : 'Off'}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Product Info */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center space-x-2">
              <Package className="h-5 w-5" />
              <span>Product Info</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <EditableField
              field="productName"
              value={productInfo.name}
              label="Product Name"
              icon={Package}
            />
            <EditableField
              field="productPrice"
              value={productInfo.price}
              label="Price"
              icon={Package}
            />
            <EditableField
              field="productDescription"
              value={productInfo.description}
              label="Description"
              icon={Package}
            />
            <div className="pt-2">
              <Label className="text-sm text-gray-600">Variants:</Label>
              <div className="flex flex-wrap gap-1 mt-1">
                {productInfo.variants.map((variant, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {variant}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
