'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useBrandingStore } from '@/store/brandingStore';
import { useWebSocket } from '@/hooks/useWebSocket';
import { Send, Loader2 } from 'lucide-react';

export function InputSection() {
  const {
    businessInput,
    updateBusinessInput,
    websocketStatus,
    updateWebSocketStatus
  } = useBrandingStore();

  const { sendMessage } = useWebSocket();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const visualStyleOptions = [
    'Modern & Minimalist',
    'Bold & Vibrant',
    'Classic & Professional',
    'Creative & Artistic',
    'Tech & Futuristic',
    'Warm & Friendly',
    'Luxury & Premium',
    'Playful & Fun'
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!businessInput.name || !businessInput.description || !businessInput.visualStyle) {
      return;
    }

    setIsSubmitting(true);

    // Send business input to WebSocket for AI processing
    sendMessage({
      type: 'generate_branding',
      data: businessInput
    });

    // Reset submitting state after a delay
    setTimeout(() => {
      setIsSubmitting(false);
    }, 1000);
  };

  const isFormValid = businessInput.name && businessInput.description && businessInput.visualStyle;

  return (
    <div className="h-full p-6 overflow-y-auto">
      <Card className="border-0 shadow-none">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-gray-900">
            Business Information
          </CardTitle>
          <p className="text-gray-600">
            Tell us about your business to generate your brand identity
          </p>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Business Name */}
            <div className="space-y-2">
              <Label htmlFor="businessName" className="text-sm font-medium text-gray-700">
                Business Name *
              </Label>
              <Input
                id="businessName"
                placeholder="Enter your business name"
                value={businessInput.name}
                onChange={(e) => updateBusinessInput({ name: e.target.value })}
                className="w-full"
                disabled={isSubmitting}
              />
            </div>

            {/* Business Description */}
            <div className="space-y-2">
              <Label htmlFor="businessDescription" className="text-sm font-medium text-gray-700">
                Business Description *
              </Label>
              <Textarea
                id="businessDescription"
                placeholder="Describe what your business does, your target audience, and key values..."
                value={businessInput.description}
                onChange={(e) => updateBusinessInput({ description: e.target.value })}
                className="w-full min-h-[120px] resize-none"
                disabled={isSubmitting}
              />
            </div>

            {/* Visual Style Preference */}
            <div className="space-y-2">
              <Label htmlFor="visualStyle" className="text-sm font-medium text-gray-700">
                Preferred Visual Style *
              </Label>
              <Select
                value={businessInput.visualStyle}
                onValueChange={(value) => updateBusinessInput({ visualStyle: value })}
                disabled={isSubmitting}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Choose your preferred style" />
                </SelectTrigger>
                <SelectContent>
                  {visualStyleOptions.map((style) => (
                    <SelectItem key={style} value={style}>
                      {style}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full"
              disabled={!isFormValid || isSubmitting}
              size="lg"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Brand Identity...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Generate Brand Identity
                </>
              )}
            </Button>
          </form>

          {/* WebSocket Status */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-2">AI Processing Status</h4>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                websocketStatus.connected ? 'bg-green-500' : 'bg-red-500'
              }`} />
              <span className="text-sm text-gray-600">
                {websocketStatus.status === 'idle' && 'Ready to generate'}
                {websocketStatus.status === 'connecting' && 'Connecting to AI...'}
                {websocketStatus.status === 'generating' && 'AI generating brand identity...'}
                {websocketStatus.status === 'completed' && websocketStatus.lastFetched && 
                  `Generated ${Math.floor((Date.now() - websocketStatus.lastFetched.getTime()) / 1000)} seconds ago`}
                {websocketStatus.status === 'error' && 'Connection failed'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
