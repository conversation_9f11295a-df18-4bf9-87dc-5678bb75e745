'use client';

import { useEffect, useRef, useCallback } from 'react';
import { useBrandingStore } from '@/store/brandingStore';

interface WebSocketMessage {
  type: 'brand_colors' | 'brand_tone' | 'brand_fonts' | 'graphic_assets' | 'product_info' | 'error';
  data: any;
}

export function useWebSocket() {
  const ws = useRef<WebSocket | null>(null);
  const {
    updateBrandColors,
    updateBrandTone,
    updateBrandFonts,
    updateGraphicAssets,
    updateProductInfo,
    updateWebSocketStatus,
    businessInput
  } = useBrandingStore();

  const connect = useCallback(() => {
    try {
      // In a real implementation, this would connect to your Flask backend
      // For now, we'll simulate the WebSocket connection
      updateWebSocketStatus({ status: 'connecting', connected: false });
      
      // Simulate connection delay
      setTimeout(() => {
        updateWebSocketStatus({ status: 'generating', connected: true });
        
        // Simulate AI processing and responses
        simulateAIResponses();
      }, 1000);
      
    } catch (error) {
      console.error('WebSocket connection failed:', error);
      updateWebSocketStatus({ status: 'error', connected: false });
    }
  }, [updateWebSocketStatus]);

  const simulateAIResponses = useCallback(() => {
    const responses = [
      {
        type: 'brand_colors',
        data: {
          primary: '#2563EB',
          secondary: '#7C3AED',
          accent: '#F59E0B',
          background: '#FFFFFF',
          text: '#1F2937'
        }
      },
      {
        type: 'brand_tone',
        data: 'Modern, Professional, and Trustworthy'
      },
      {
        type: 'brand_fonts',
        data: {
          primary: 'Inter',
          secondary: 'Roboto'
        }
      },
      {
        type: 'graphic_assets',
        data: {
          useGradients: true,
          useGlowEffects: true,
          useThickBorders: false,
          useBackgroundTexture: false,
          useGlassmorphism: true
        }
      },
      {
        type: 'product_info',
        data: {
          name: businessInput.name ? `${businessInput.name} Premium` : 'Premium Product',
          price: '$149.99',
          variants: ['Starter', 'Professional', 'Enterprise'],
          description: `High-quality solution designed for ${businessInput.visualStyle?.toLowerCase() || 'modern'} businesses`
        }
      }
    ];

    // Send responses with delays to simulate real AI processing
    responses.forEach((response, index) => {
      setTimeout(() => {
        handleMessage(response);
      }, (index + 1) * 800);
    });

    // Mark as completed after all responses
    setTimeout(() => {
      updateWebSocketStatus({ 
        status: 'completed', 
        lastFetched: new Date(),
        connected: true 
      });
    }, responses.length * 800 + 500);
  }, [businessInput, updateBrandColors, updateBrandTone, updateBrandFonts, updateGraphicAssets, updateProductInfo, updateWebSocketStatus]);

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'brand_colors':
        updateBrandColors(message.data);
        break;
      case 'brand_tone':
        updateBrandTone(message.data);
        break;
      case 'brand_fonts':
        updateBrandFonts(message.data);
        break;
      case 'graphic_assets':
        updateGraphicAssets(message.data);
        break;
      case 'product_info':
        updateProductInfo(message.data);
        break;
      case 'error':
        console.error('WebSocket error:', message.data);
        updateWebSocketStatus({ status: 'error', connected: false });
        break;
    }
  }, [updateBrandColors, updateBrandTone, updateBrandFonts, updateGraphicAssets, updateProductInfo, updateWebSocketStatus]);

  const sendMessage = useCallback((message: any) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify(message));
    } else {
      // For simulation, we'll trigger the connect function
      connect();
    }
  }, [connect]);

  const disconnect = useCallback(() => {
    if (ws.current) {
      ws.current.close();
      ws.current = null;
    }
    updateWebSocketStatus({ connected: false, status: 'idle' });
  }, [updateWebSocketStatus]);

  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    connect,
    disconnect,
    sendMessage,
    isConnected: ws.current?.readyState === WebSocket.OPEN
  };
}

// Real WebSocket implementation for Flask backend
export function useRealWebSocket(url: string = 'ws://localhost:5000/ws') {
  const ws = useRef<WebSocket | null>(null);
  const {
    updateBrandColors,
    updateBrandTone,
    updateBrandFonts,
    updateGraphicAssets,
    updateProductInfo,
    updateWebSocketStatus
  } = useBrandingStore();

  const connect = useCallback(() => {
    try {
      ws.current = new WebSocket(url);
      
      ws.current.onopen = () => {
        console.log('WebSocket connected');
        updateWebSocketStatus({ connected: true, status: 'idle' });
      };

      ws.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          
          switch (message.type) {
            case 'brand_colors':
              updateBrandColors(message.data);
              break;
            case 'brand_tone':
              updateBrandTone(message.data);
              break;
            case 'brand_fonts':
              updateBrandFonts(message.data);
              break;
            case 'graphic_assets':
              updateGraphicAssets(message.data);
              break;
            case 'product_info':
              updateProductInfo(message.data);
              updateWebSocketStatus({ 
                status: 'completed', 
                lastFetched: new Date() 
              });
              break;
            case 'error':
              console.error('WebSocket error:', message.data);
              updateWebSocketStatus({ status: 'error' });
              break;
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      ws.current.onclose = () => {
        console.log('WebSocket disconnected');
        updateWebSocketStatus({ connected: false, status: 'idle' });
      };

      ws.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        updateWebSocketStatus({ status: 'error', connected: false });
      };

    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      updateWebSocketStatus({ status: 'error', connected: false });
    }
  }, [url, updateBrandColors, updateBrandTone, updateBrandFonts, updateGraphicAssets, updateProductInfo, updateWebSocketStatus]);

  const sendMessage = useCallback((message: any) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify(message));
      updateWebSocketStatus({ status: 'generating' });
    }
  }, [updateWebSocketStatus]);

  const disconnect = useCallback(() => {
    if (ws.current) {
      ws.current.close();
      ws.current = null;
    }
  }, []);

  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    connect,
    disconnect,
    sendMessage,
    isConnected: ws.current?.readyState === WebSocket.OPEN
  };
}
