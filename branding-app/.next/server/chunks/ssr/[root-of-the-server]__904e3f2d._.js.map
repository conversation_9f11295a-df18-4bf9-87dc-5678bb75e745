{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/store/brandingStore.ts"], "sourcesContent": ["import { create } from 'zustand';\n\nexport interface BrandColors {\n  primary: string;\n  secondary: string;\n  accent: string;\n  background: string;\n  text: string;\n}\n\nexport interface BrandFonts {\n  primary: string;\n  secondary: string;\n}\n\nexport interface GraphicAssets {\n  useGradients: boolean;\n  useGlowEffects: boolean;\n  useThickBorders: boolean;\n  useBackgroundTexture: boolean;\n  useGlassmorphism: boolean;\n}\n\nexport interface ProductInfo {\n  name: string;\n  price: string;\n  variants: string[];\n  description: string;\n}\n\nexport interface BusinessInput {\n  name: string;\n  description: string;\n  visualStyle: string;\n}\n\nexport interface WebSocketStatus {\n  connected: boolean;\n  lastFetched: Date | null;\n  status: 'idle' | 'connecting' | 'generating' | 'completed' | 'error';\n}\n\nexport interface BrandingState {\n  // Input data\n  businessInput: BusinessInput;\n  \n  // AI Generated data\n  brandColors: BrandColors;\n  brandTone: string;\n  brandFonts: BrandFonts;\n  graphicAssets: GraphicAssets;\n  productInfo: ProductInfo;\n  \n  // WebSocket status\n  websocketStatus: WebSocketStatus;\n  \n  // Actions\n  updateBusinessInput: (input: Partial<BusinessInput>) => void;\n  updateBrandColors: (colors: Partial<BrandColors>) => void;\n  updateBrandTone: (tone: string) => void;\n  updateBrandFonts: (fonts: Partial<BrandFonts>) => void;\n  updateGraphicAssets: (assets: Partial<GraphicAssets>) => void;\n  updateProductInfo: (info: Partial<ProductInfo>) => void;\n  updateWebSocketStatus: (status: Partial<WebSocketStatus>) => void;\n  resetBrandingData: () => void;\n}\n\nconst initialState = {\n  businessInput: {\n    name: '',\n    description: '',\n    visualStyle: '',\n  },\n  brandColors: {\n    primary: '#3B82F6',\n    secondary: '#8B5CF6',\n    accent: '#F59E0B',\n    background: '#FFFFFF',\n    text: '#1F2937',\n  },\n  brandTone: 'Professional and Modern',\n  brandFonts: {\n    primary: 'Inter',\n    secondary: 'Roboto',\n  },\n  graphicAssets: {\n    useGradients: true,\n    useGlowEffects: false,\n    useThickBorders: false,\n    useBackgroundTexture: false,\n    useGlassmorphism: true,\n  },\n  productInfo: {\n    name: 'Premium Product',\n    price: '$99.99',\n    variants: ['Standard', 'Premium', 'Enterprise'],\n    description: 'A high-quality product designed for modern businesses',\n  },\n  websocketStatus: {\n    connected: false,\n    lastFetched: null,\n    status: 'idle' as const,\n  },\n};\n\nexport const useBrandingStore = create<BrandingState>((set) => ({\n  ...initialState,\n  \n  updateBusinessInput: (input) =>\n    set((state) => ({\n      businessInput: { ...state.businessInput, ...input },\n    })),\n    \n  updateBrandColors: (colors) =>\n    set((state) => ({\n      brandColors: { ...state.brandColors, ...colors },\n    })),\n    \n  updateBrandTone: (tone) => set({ brandTone: tone }),\n  \n  updateBrandFonts: (fonts) =>\n    set((state) => ({\n      brandFonts: { ...state.brandFonts, ...fonts },\n    })),\n    \n  updateGraphicAssets: (assets) =>\n    set((state) => ({\n      graphicAssets: { ...state.graphicAssets, ...assets },\n    })),\n    \n  updateProductInfo: (info) =>\n    set((state) => ({\n      productInfo: { ...state.productInfo, ...info },\n    })),\n    \n  updateWebSocketStatus: (status) =>\n    set((state) => ({\n      websocketStatus: { ...state.websocketStatus, ...status },\n    })),\n    \n  resetBrandingData: () => set(initialState),\n}));\n"], "names": [], "mappings": ";;;AAAA;;AAmEA,MAAM,eAAe;IACnB,eAAe;QACb,MAAM;QACN,aAAa;QACb,aAAa;IACf;IACA,aAAa;QACX,SAAS;QACT,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,MAAM;IACR;IACA,WAAW;IACX,YAAY;QACV,SAAS;QACT,WAAW;IACb;IACA,eAAe;QACb,cAAc;QACd,gBAAgB;QAChB,iBAAiB;QACjB,sBAAsB;QACtB,kBAAkB;IACpB;IACA,aAAa;QACX,MAAM;QACN,OAAO;QACP,UAAU;YAAC;YAAY;YAAW;SAAa;QAC/C,aAAa;IACf;IACA,iBAAiB;QACf,WAAW;QACX,aAAa;QACb,QAAQ;IACV;AACF;AAEO,MAAM,mBAAmB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,MAAQ,CAAC;QAC9D,GAAG,YAAY;QAEf,qBAAqB,CAAC,QACpB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;wBAAE,GAAG,MAAM,aAAa;wBAAE,GAAG,KAAK;oBAAC;gBACpD,CAAC;QAEH,mBAAmB,CAAC,SAClB,IAAI,CAAC,QAAU,CAAC;oBACd,aAAa;wBAAE,GAAG,MAAM,WAAW;wBAAE,GAAG,MAAM;oBAAC;gBACjD,CAAC;QAEH,iBAAiB,CAAC,OAAS,IAAI;gBAAE,WAAW;YAAK;QAEjD,kBAAkB,CAAC,QACjB,IAAI,CAAC,QAAU,CAAC;oBACd,YAAY;wBAAE,GAAG,MAAM,UAAU;wBAAE,GAAG,KAAK;oBAAC;gBAC9C,CAAC;QAEH,qBAAqB,CAAC,SACpB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;wBAAE,GAAG,MAAM,aAAa;wBAAE,GAAG,MAAM;oBAAC;gBACrD,CAAC;QAEH,mBAAmB,CAAC,OAClB,IAAI,CAAC,QAAU,CAAC;oBACd,aAAa;wBAAE,GAAG,MAAM,WAAW;wBAAE,GAAG,IAAI;oBAAC;gBAC/C,CAAC;QAEH,uBAAuB,CAAC,SACtB,IAAI,CAAC,QAAU,CAAC;oBACd,iBAAiB;wBAAE,GAAG,MAAM,eAAe;wBAAE,GAAG,MAAM;oBAAC;gBACzD,CAAC;QAEH,mBAAmB,IAAM,IAAI;IAC/B,CAAC", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/hooks/useWebSocket.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useCallback } from 'react';\nimport { useBrandingStore } from '@/store/brandingStore';\n\ninterface WebSocketMessage {\n  type: 'brand_colors' | 'brand_tone' | 'brand_fonts' | 'graphic_assets' | 'product_info' | 'error';\n  data: any;\n}\n\nexport function useWebSocket() {\n  const ws = useRef<WebSocket | null>(null);\n  const {\n    updateBrandColors,\n    updateBrandTone,\n    updateBrandFonts,\n    updateGraphicAssets,\n    updateProductInfo,\n    updateWebSocketStatus,\n    businessInput\n  } = useBrandingStore();\n\n  const connect = useCallback(() => {\n    try {\n      // In a real implementation, this would connect to your Flask backend\n      // For now, we'll simulate the WebSocket connection\n      updateWebSocketStatus({ status: 'connecting', connected: false });\n      \n      // Simulate connection delay\n      setTimeout(() => {\n        updateWebSocketStatus({ status: 'generating', connected: true });\n        \n        // Simulate AI processing and responses\n        simulateAIResponses();\n      }, 1000);\n      \n    } catch (error) {\n      console.error('WebSocket connection failed:', error);\n      updateWebSocketStatus({ status: 'error', connected: false });\n    }\n  }, [updateWebSocketStatus]);\n\n  const simulateAIResponses = useCallback(() => {\n    const responses = [\n      {\n        type: 'brand_colors',\n        data: {\n          primary: '#2563EB',\n          secondary: '#7C3AED',\n          accent: '#F59E0B',\n          background: '#FFFFFF',\n          text: '#1F2937'\n        }\n      },\n      {\n        type: 'brand_tone',\n        data: 'Modern, Professional, and Trustworthy'\n      },\n      {\n        type: 'brand_fonts',\n        data: {\n          primary: 'Inter',\n          secondary: 'Roboto'\n        }\n      },\n      {\n        type: 'graphic_assets',\n        data: {\n          useGradients: true,\n          useGlowEffects: true,\n          useThickBorders: false,\n          useBackgroundTexture: false,\n          useGlassmorphism: true\n        }\n      },\n      {\n        type: 'product_info',\n        data: {\n          name: businessInput.name ? `${businessInput.name} Premium` : 'Premium Product',\n          price: '$149.99',\n          variants: ['Starter', 'Professional', 'Enterprise'],\n          description: `High-quality solution designed for ${businessInput.visualStyle?.toLowerCase() || 'modern'} businesses`\n        }\n      }\n    ];\n\n    // Send responses with delays to simulate real AI processing\n    responses.forEach((response, index) => {\n      setTimeout(() => {\n        handleMessage(response);\n      }, (index + 1) * 800);\n    });\n\n    // Mark as completed after all responses\n    setTimeout(() => {\n      updateWebSocketStatus({ \n        status: 'completed', \n        lastFetched: new Date(),\n        connected: true \n      });\n    }, responses.length * 800 + 500);\n  }, [businessInput, updateBrandColors, updateBrandTone, updateBrandFonts, updateGraphicAssets, updateProductInfo, updateWebSocketStatus]);\n\n  const handleMessage = useCallback((message: WebSocketMessage) => {\n    switch (message.type) {\n      case 'brand_colors':\n        updateBrandColors(message.data);\n        break;\n      case 'brand_tone':\n        updateBrandTone(message.data);\n        break;\n      case 'brand_fonts':\n        updateBrandFonts(message.data);\n        break;\n      case 'graphic_assets':\n        updateGraphicAssets(message.data);\n        break;\n      case 'product_info':\n        updateProductInfo(message.data);\n        break;\n      case 'error':\n        console.error('WebSocket error:', message.data);\n        updateWebSocketStatus({ status: 'error', connected: false });\n        break;\n    }\n  }, [updateBrandColors, updateBrandTone, updateBrandFonts, updateGraphicAssets, updateProductInfo, updateWebSocketStatus]);\n\n  const sendMessage = useCallback((message: any) => {\n    if (ws.current && ws.current.readyState === WebSocket.OPEN) {\n      ws.current.send(JSON.stringify(message));\n    } else {\n      // For simulation, we'll trigger the connect function\n      connect();\n    }\n  }, [connect]);\n\n  const disconnect = useCallback(() => {\n    if (ws.current) {\n      ws.current.close();\n      ws.current = null;\n    }\n    updateWebSocketStatus({ connected: false, status: 'idle' });\n  }, [updateWebSocketStatus]);\n\n  useEffect(() => {\n    return () => {\n      disconnect();\n    };\n  }, [disconnect]);\n\n  return {\n    connect,\n    disconnect,\n    sendMessage,\n    isConnected: ws.current?.readyState === WebSocket.OPEN\n  };\n}\n\n// Real WebSocket implementation for Flask backend\nexport function useRealWebSocket(url: string = 'ws://localhost:5000/ws') {\n  const ws = useRef<WebSocket | null>(null);\n  const {\n    updateBrandColors,\n    updateBrandTone,\n    updateBrandFonts,\n    updateGraphicAssets,\n    updateProductInfo,\n    updateWebSocketStatus\n  } = useBrandingStore();\n\n  const connect = useCallback(() => {\n    try {\n      ws.current = new WebSocket(url);\n      \n      ws.current.onopen = () => {\n        console.log('WebSocket connected');\n        updateWebSocketStatus({ connected: true, status: 'idle' });\n      };\n\n      ws.current.onmessage = (event) => {\n        try {\n          const message: WebSocketMessage = JSON.parse(event.data);\n          \n          switch (message.type) {\n            case 'brand_colors':\n              updateBrandColors(message.data);\n              break;\n            case 'brand_tone':\n              updateBrandTone(message.data);\n              break;\n            case 'brand_fonts':\n              updateBrandFonts(message.data);\n              break;\n            case 'graphic_assets':\n              updateGraphicAssets(message.data);\n              break;\n            case 'product_info':\n              updateProductInfo(message.data);\n              updateWebSocketStatus({ \n                status: 'completed', \n                lastFetched: new Date() \n              });\n              break;\n            case 'error':\n              console.error('WebSocket error:', message.data);\n              updateWebSocketStatus({ status: 'error' });\n              break;\n          }\n        } catch (error) {\n          console.error('Failed to parse WebSocket message:', error);\n        }\n      };\n\n      ws.current.onclose = () => {\n        console.log('WebSocket disconnected');\n        updateWebSocketStatus({ connected: false, status: 'idle' });\n      };\n\n      ws.current.onerror = (error) => {\n        console.error('WebSocket error:', error);\n        updateWebSocketStatus({ status: 'error', connected: false });\n      };\n\n    } catch (error) {\n      console.error('Failed to connect WebSocket:', error);\n      updateWebSocketStatus({ status: 'error', connected: false });\n    }\n  }, [url, updateBrandColors, updateBrandTone, updateBrandFonts, updateGraphicAssets, updateProductInfo, updateWebSocketStatus]);\n\n  const sendMessage = useCallback((message: any) => {\n    if (ws.current && ws.current.readyState === WebSocket.OPEN) {\n      ws.current.send(JSON.stringify(message));\n      updateWebSocketStatus({ status: 'generating' });\n    }\n  }, [updateWebSocketStatus]);\n\n  const disconnect = useCallback(() => {\n    if (ws.current) {\n      ws.current.close();\n      ws.current = null;\n    }\n  }, []);\n\n  useEffect(() => {\n    return () => {\n      disconnect();\n    };\n  }, [disconnect]);\n\n  return {\n    connect,\n    disconnect,\n    sendMessage,\n    isConnected: ws.current?.readyState === WebSocket.OPEN\n  };\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAUO,SAAS;IACd,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IACpC,MAAM,EACJ,iBAAiB,EACjB,eAAe,EACf,gBAAgB,EAChB,mBAAmB,EACnB,iBAAiB,EACjB,qBAAqB,EACrB,aAAa,EACd,GAAG,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,IAAI;YACF,qEAAqE;YACrE,mDAAmD;YACnD,sBAAsB;gBAAE,QAAQ;gBAAc,WAAW;YAAM;YAE/D,4BAA4B;YAC5B,WAAW;gBACT,sBAAsB;oBAAE,QAAQ;oBAAc,WAAW;gBAAK;gBAE9D,uCAAuC;gBACvC;YACF,GAAG;QAEL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,sBAAsB;gBAAE,QAAQ;gBAAS,WAAW;YAAM;QAC5D;IACF,GAAG;QAAC;KAAsB;IAE1B,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,MAAM,YAAY;YAChB;gBACE,MAAM;gBACN,MAAM;oBACJ,SAAS;oBACT,WAAW;oBACX,QAAQ;oBACR,YAAY;oBACZ,MAAM;gBACR;YACF;YACA;gBACE,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;oBACJ,SAAS;oBACT,WAAW;gBACb;YACF;YACA;gBACE,MAAM;gBACN,MAAM;oBACJ,cAAc;oBACd,gBAAgB;oBAChB,iBAAiB;oBACjB,sBAAsB;oBACtB,kBAAkB;gBACpB;YACF;YACA;gBACE,MAAM;gBACN,MAAM;oBACJ,MAAM,cAAc,IAAI,GAAG,GAAG,cAAc,IAAI,CAAC,QAAQ,CAAC,GAAG;oBAC7D,OAAO;oBACP,UAAU;wBAAC;wBAAW;wBAAgB;qBAAa;oBACnD,aAAa,CAAC,mCAAmC,EAAE,cAAc,WAAW,EAAE,iBAAiB,SAAS,WAAW,CAAC;gBACtH;YACF;SACD;QAED,4DAA4D;QAC5D,UAAU,OAAO,CAAC,CAAC,UAAU;YAC3B,WAAW;gBACT,cAAc;YAChB,GAAG,CAAC,QAAQ,CAAC,IAAI;QACnB;QAEA,wCAAwC;QACxC,WAAW;YACT,sBAAsB;gBACpB,QAAQ;gBACR,aAAa,IAAI;gBACjB,WAAW;YACb;QACF,GAAG,UAAU,MAAM,GAAG,MAAM;IAC9B,GAAG;QAAC;QAAe;QAAmB;QAAiB;QAAkB;QAAqB;QAAmB;KAAsB;IAEvI,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,kBAAkB,QAAQ,IAAI;gBAC9B;YACF,KAAK;gBACH,gBAAgB,QAAQ,IAAI;gBAC5B;YACF,KAAK;gBACH,iBAAiB,QAAQ,IAAI;gBAC7B;YACF,KAAK;gBACH,oBAAoB,QAAQ,IAAI;gBAChC;YACF,KAAK;gBACH,kBAAkB,QAAQ,IAAI;gBAC9B;YACF,KAAK;gBACH,QAAQ,KAAK,CAAC,oBAAoB,QAAQ,IAAI;gBAC9C,sBAAsB;oBAAE,QAAQ;oBAAS,WAAW;gBAAM;gBAC1D;QACJ;IACF,GAAG;QAAC;QAAmB;QAAiB;QAAkB;QAAqB;QAAmB;KAAsB;IAExH,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,IAAI,GAAG,OAAO,IAAI,GAAG,OAAO,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YAC1D,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;QACjC,OAAO;YACL,qDAAqD;YACrD;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,GAAG,OAAO,EAAE;YACd,GAAG,OAAO,CAAC,KAAK;YAChB,GAAG,OAAO,GAAG;QACf;QACA,sBAAsB;YAAE,WAAW;YAAO,QAAQ;QAAO;IAC3D,GAAG;QAAC;KAAsB;IAE1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA;QACA;QACA,aAAa,GAAG,OAAO,EAAE,eAAe,UAAU,IAAI;IACxD;AACF;AAGO,SAAS,iBAAiB,MAAc,wBAAwB;IACrE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IACpC,MAAM,EACJ,iBAAiB,EACjB,eAAe,EACf,gBAAgB,EAChB,mBAAmB,EACnB,iBAAiB,EACjB,qBAAqB,EACtB,GAAG,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC1B,IAAI;YACF,GAAG,OAAO,GAAG,IAAI,UAAU;YAE3B,GAAG,OAAO,CAAC,MAAM,GAAG;gBAClB,QAAQ,GAAG,CAAC;gBACZ,sBAAsB;oBAAE,WAAW;oBAAM,QAAQ;gBAAO;YAC1D;YAEA,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC;gBACtB,IAAI;oBACF,MAAM,UAA4B,KAAK,KAAK,CAAC,MAAM,IAAI;oBAEvD,OAAQ,QAAQ,IAAI;wBAClB,KAAK;4BACH,kBAAkB,QAAQ,IAAI;4BAC9B;wBACF,KAAK;4BACH,gBAAgB,QAAQ,IAAI;4BAC5B;wBACF,KAAK;4BACH,iBAAiB,QAAQ,IAAI;4BAC7B;wBACF,KAAK;4BACH,oBAAoB,QAAQ,IAAI;4BAChC;wBACF,KAAK;4BACH,kBAAkB,QAAQ,IAAI;4BAC9B,sBAAsB;gCACpB,QAAQ;gCACR,aAAa,IAAI;4BACnB;4BACA;wBACF,KAAK;4BACH,QAAQ,KAAK,CAAC,oBAAoB,QAAQ,IAAI;4BAC9C,sBAAsB;gCAAE,QAAQ;4BAAQ;4BACxC;oBACJ;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,sCAAsC;gBACtD;YACF;YAEA,GAAG,OAAO,CAAC,OAAO,GAAG;gBACnB,QAAQ,GAAG,CAAC;gBACZ,sBAAsB;oBAAE,WAAW;oBAAO,QAAQ;gBAAO;YAC3D;YAEA,GAAG,OAAO,CAAC,OAAO,GAAG,CAAC;gBACpB,QAAQ,KAAK,CAAC,oBAAoB;gBAClC,sBAAsB;oBAAE,QAAQ;oBAAS,WAAW;gBAAM;YAC5D;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,sBAAsB;gBAAE,QAAQ;gBAAS,WAAW;YAAM;QAC5D;IACF,GAAG;QAAC;QAAK;QAAmB;QAAiB;QAAkB;QAAqB;QAAmB;KAAsB;IAE7H,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,IAAI,GAAG,OAAO,IAAI,GAAG,OAAO,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YAC1D,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;YAC/B,sBAAsB;gBAAE,QAAQ;YAAa;QAC/C;IACF,GAAG;QAAC;KAAsB;IAE1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,GAAG,OAAO,EAAE;YACd,GAAG,OAAO,CAAC,KAAK;YAChB,GAAG,OAAO,GAAG;QACf;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAW;IAEf,OAAO;QACL;QACA;QACA;QACA,aAAa,GAAG,OAAO,EAAE,eAAe,UAAU,IAAI;IACxD;AACF", "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/components/InputSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { useBrandingStore } from '@/store/brandingStore';\nimport { useWebSocket } from '@/hooks/useWebSocket';\nimport { Send, Loader2 } from 'lucide-react';\n\nexport function InputSection() {\n  const {\n    businessInput,\n    updateBusinessInput,\n    websocketStatus,\n    updateWebSocketStatus\n  } = useBrandingStore();\n\n  const { sendMessage } = useWebSocket();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const visualStyleOptions = [\n    'Modern & Minimalist',\n    'Bold & Vibrant',\n    'Classic & Professional',\n    'Creative & Artistic',\n    'Tech & Futuristic',\n    'Warm & Friendly',\n    'Luxury & Premium',\n    'Playful & Fun'\n  ];\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!businessInput.name || !businessInput.description || !businessInput.visualStyle) {\n      return;\n    }\n\n    setIsSubmitting(true);\n\n    // Send business input to WebSocket for AI processing\n    sendMessage({\n      type: 'generate_branding',\n      data: businessInput\n    });\n\n    // Reset submitting state after a delay\n    setTimeout(() => {\n      setIsSubmitting(false);\n    }, 1000);\n  };\n\n  const isFormValid = businessInput.name && businessInput.description && businessInput.visualStyle;\n\n  return (\n    <div className=\"h-full p-6 overflow-y-auto\">\n      <Card className=\"border-0 shadow-none\">\n        <CardHeader>\n          <CardTitle className=\"text-2xl font-bold text-gray-900\">\n            Business Information\n          </CardTitle>\n          <p className=\"text-gray-600\">\n            Tell us about your business to generate your brand identity\n          </p>\n        </CardHeader>\n        \n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Business Name */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"businessName\" className=\"text-sm font-medium text-gray-700\">\n                Business Name *\n              </Label>\n              <Input\n                id=\"businessName\"\n                placeholder=\"Enter your business name\"\n                value={businessInput.name}\n                onChange={(e) => updateBusinessInput({ name: e.target.value })}\n                className=\"w-full\"\n                disabled={isSubmitting}\n              />\n            </div>\n\n            {/* Business Description */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"businessDescription\" className=\"text-sm font-medium text-gray-700\">\n                Business Description *\n              </Label>\n              <Textarea\n                id=\"businessDescription\"\n                placeholder=\"Describe what your business does, your target audience, and key values...\"\n                value={businessInput.description}\n                onChange={(e) => updateBusinessInput({ description: e.target.value })}\n                className=\"w-full min-h-[120px] resize-none\"\n                disabled={isSubmitting}\n              />\n            </div>\n\n            {/* Visual Style Preference */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"visualStyle\" className=\"text-sm font-medium text-gray-700\">\n                Preferred Visual Style *\n              </Label>\n              <Select\n                value={businessInput.visualStyle}\n                onValueChange={(value) => updateBusinessInput({ visualStyle: value })}\n                disabled={isSubmitting}\n              >\n                <SelectTrigger className=\"w-full\">\n                  <SelectValue placeholder=\"Choose your preferred style\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {visualStyleOptions.map((style) => (\n                    <SelectItem key={style} value={style}>\n                      {style}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n            </div>\n\n            {/* Submit Button */}\n            <Button\n              type=\"submit\"\n              className=\"w-full\"\n              disabled={!isFormValid || isSubmitting}\n              size=\"lg\"\n            >\n              {isSubmitting ? (\n                <>\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                  Generating Brand Identity...\n                </>\n              ) : (\n                <>\n                  <Send className=\"mr-2 h-4 w-4\" />\n                  Generate Brand Identity\n                </>\n              )}\n            </Button>\n          </form>\n\n          {/* WebSocket Status */}\n          <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\n            <h4 className=\"text-sm font-medium text-gray-700 mb-2\">AI Processing Status</h4>\n            <div className=\"flex items-center space-x-2\">\n              <div className={`w-2 h-2 rounded-full ${\n                websocketStatus.connected ? 'bg-green-500' : 'bg-red-500'\n              }`} />\n              <span className=\"text-sm text-gray-600\">\n                {websocketStatus.status === 'idle' && 'Ready to generate'}\n                {websocketStatus.status === 'connecting' && 'Connecting to AI...'}\n                {websocketStatus.status === 'generating' && 'AI generating brand identity...'}\n                {websocketStatus.status === 'completed' && websocketStatus.lastFetched && \n                  `Generated ${Math.floor((Date.now() - websocketStatus.lastFetched.getTime()) / 1000)} seconds ago`}\n                {websocketStatus.status === 'error' && 'Connection failed'}\n              </span>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAXA;;;;;;;;;;;;AAaO,SAAS;IACd,MAAM,EACJ,aAAa,EACb,mBAAmB,EACnB,eAAe,EACf,qBAAqB,EACtB,GAAG,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,WAAW,IAAI,CAAC,cAAc,WAAW,EAAE;YACnF;QACF;QAEA,gBAAgB;QAEhB,qDAAqD;QACrD,YAAY;YACV,MAAM;YACN,MAAM;QACR;QAEA,uCAAuC;QACvC,WAAW;YACT,gBAAgB;QAClB,GAAG;IACL;IAEA,MAAM,cAAc,cAAc,IAAI,IAAI,cAAc,WAAW,IAAI,cAAc,WAAW;IAEhG,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;;sCACT,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAmC;;;;;;sCAGxD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAK/B,8OAAC,gIAAA,CAAA,cAAW;;sCACV,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAe,WAAU;sDAAoC;;;;;;sDAG5E,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,aAAY;4CACZ,OAAO,cAAc,IAAI;4CACzB,UAAU,CAAC,IAAM,oBAAoB;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC5D,WAAU;4CACV,UAAU;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAsB,WAAU;sDAAoC;;;;;;sDAGnF,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,aAAY;4CACZ,OAAO,cAAc,WAAW;4CAChC,UAAU,CAAC,IAAM,oBAAoB;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACnE,WAAU;4CACV,UAAU;;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAc,WAAU;sDAAoC;;;;;;sDAG3E,8OAAC,kIAAA,CAAA,SAAM;4CACL,OAAO,cAAc,WAAW;4CAChC,eAAe,CAAC,QAAU,oBAAoB;oDAAE,aAAa;gDAAM;4CACnE,UAAU;;8DAEV,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;8DACX,mBAAmB,GAAG,CAAC,CAAC,sBACvB,8OAAC,kIAAA,CAAA,aAAU;4DAAa,OAAO;sEAC5B;2DADc;;;;;;;;;;;;;;;;;;;;;;8CASzB,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,UAAU,CAAC,eAAe;oCAC1B,MAAK;8CAEJ,6BACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;qEAInD;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;sCAQzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,qBAAqB,EACpC,gBAAgB,SAAS,GAAG,iBAAiB,cAC7C;;;;;;sDACF,8OAAC;4CAAK,WAAU;;gDACb,gBAAgB,MAAM,KAAK,UAAU;gDACrC,gBAAgB,MAAM,KAAK,gBAAgB;gDAC3C,gBAAgB,MAAM,KAAK,gBAAgB;gDAC3C,gBAAgB,MAAM,KAAK,eAAe,gBAAgB,WAAW,IACpE,CAAC,UAAU,EAAE,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,gBAAgB,WAAW,CAAC,OAAO,EAAE,IAAI,MAAM,YAAY,CAAC;gDACnG,gBAAgB,MAAM,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD", "debugId": null}}, {"offset": {"line": 1201, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1247, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/components/BrandingDataSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Badge } from '@/components/ui/badge';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { useBrandingStore } from '@/store/brandingStore';\nimport { Palette, Type, Sparkles, Package, Edit3, Check, X } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\n\nexport function BrandingDataSection() {\n  const {\n    brandColors,\n    brandTone,\n    brandFonts,\n    graphicAssets,\n    productInfo,\n    updateBrandColors,\n    updateBrandTone,\n    updateBrandFonts,\n    updateGraphicAssets,\n    updateProductInfo,\n    websocketStatus\n  } = useBrandingStore();\n\n  const [editingField, setEditingField] = useState<string | null>(null);\n  const [tempValue, setTempValue] = useState<string>('');\n\n  const handleEdit = (field: string, currentValue: string) => {\n    setEditingField(field);\n    setTempValue(currentValue);\n  };\n\n  const handleSave = (field: string) => {\n    switch (field) {\n      case 'brandTone':\n        updateBrandTone(tempValue);\n        break;\n      case 'primaryFont':\n        updateBrandFonts({ primary: tempValue });\n        break;\n      case 'secondaryFont':\n        updateBrandFonts({ secondary: tempValue });\n        break;\n      case 'productName':\n        updateProductInfo({ name: tempValue });\n        break;\n      case 'productPrice':\n        updateProductInfo({ price: tempValue });\n        break;\n      case 'productDescription':\n        updateProductInfo({ description: tempValue });\n        break;\n    }\n    setEditingField(null);\n    setTempValue('');\n  };\n\n  const handleCancel = () => {\n    setEditingField(null);\n    setTempValue('');\n  };\n\n  const EditableField = ({ \n    field, \n    value, \n    label, \n    icon: Icon \n  }: { \n    field: string; \n    value: string; \n    label: string; \n    icon: any;\n  }) => (\n    <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n      <div className=\"flex items-center space-x-2\">\n        <Icon className=\"h-4 w-4 text-gray-500\" />\n        <span className=\"text-sm font-medium text-gray-700\">{label}:</span>\n      </div>\n      <div className=\"flex items-center space-x-2\">\n        {editingField === field ? (\n          <>\n            <Input\n              value={tempValue}\n              onChange={(e) => setTempValue(e.target.value)}\n              className=\"h-8 w-32 text-sm\"\n              autoFocus\n            />\n            <Button size=\"sm\" variant=\"ghost\" onClick={() => handleSave(field)}>\n              <Check className=\"h-3 w-3\" />\n            </Button>\n            <Button size=\"sm\" variant=\"ghost\" onClick={handleCancel}>\n              <X className=\"h-3 w-3\" />\n            </Button>\n          </>\n        ) : (\n          <>\n            <span className=\"text-sm text-gray-900\">{value}</span>\n            <Button \n              size=\"sm\" \n              variant=\"ghost\" \n              onClick={() => handleEdit(field, value)}\n              className=\"h-6 w-6 p-0\"\n            >\n              <Edit3 className=\"h-3 w-3\" />\n            </Button>\n          </>\n        )}\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"p-6\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        \n        {/* Brand Colors */}\n        <Card>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"text-lg flex items-center space-x-2\">\n              <Palette className=\"h-5 w-5\" />\n              <span>Brand Colors</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            {Object.entries(brandColors).map(([key, color]) => (\n              <div key={key} className=\"flex items-center justify-between\">\n                <span className=\"text-sm capitalize text-gray-600\">{key}</span>\n                <div className=\"flex items-center space-x-2\">\n                  <div \n                    className=\"w-6 h-6 rounded border border-gray-200\"\n                    style={{ backgroundColor: color }}\n                  />\n                  <Input\n                    value={color}\n                    onChange={(e) => updateBrandColors({ [key]: e.target.value })}\n                    className=\"h-8 w-20 text-xs font-mono\"\n                  />\n                </div>\n              </div>\n            ))}\n          </CardContent>\n        </Card>\n\n        {/* Brand Tone & Fonts */}\n        <Card>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"text-lg flex items-center space-x-2\">\n              <Type className=\"h-5 w-5\" />\n              <span>Typography</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            <EditableField\n              field=\"brandTone\"\n              value={brandTone}\n              label=\"Brand Tone\"\n              icon={Sparkles}\n            />\n            <EditableField\n              field=\"primaryFont\"\n              value={brandFonts.primary}\n              label=\"Primary Font\"\n              icon={Type}\n            />\n            <EditableField\n              field=\"secondaryFont\"\n              value={brandFonts.secondary}\n              label=\"Secondary Font\"\n              icon={Type}\n            />\n          </CardContent>\n        </Card>\n\n        {/* Graphic Assets */}\n        <Card>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"text-lg flex items-center space-x-2\">\n              <Sparkles className=\"h-5 w-5\" />\n              <span>Visual Effects</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-2\">\n            {Object.entries(graphicAssets).map(([key, enabled]) => (\n              <div key={key} className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">\n                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}\n                </span>\n                <Badge \n                  variant={enabled ? \"default\" : \"secondary\"}\n                  className=\"cursor-pointer\"\n                  onClick={() => updateGraphicAssets({ [key]: !enabled })}\n                >\n                  {enabled ? 'On' : 'Off'}\n                </Badge>\n              </div>\n            ))}\n          </CardContent>\n        </Card>\n\n        {/* Product Info */}\n        <Card>\n          <CardHeader className=\"pb-3\">\n            <CardTitle className=\"text-lg flex items-center space-x-2\">\n              <Package className=\"h-5 w-5\" />\n              <span>Product Info</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            <EditableField\n              field=\"productName\"\n              value={productInfo.name}\n              label=\"Product Name\"\n              icon={Package}\n            />\n            <EditableField\n              field=\"productPrice\"\n              value={productInfo.price}\n              label=\"Price\"\n              icon={Package}\n            />\n            <EditableField\n              field=\"productDescription\"\n              value={productInfo.description}\n              label=\"Description\"\n              icon={Package}\n            />\n            <div className=\"pt-2\">\n              <Label className=\"text-sm text-gray-600\">Variants:</Label>\n              <div className=\"flex flex-wrap gap-1 mt-1\">\n                {productInfo.variants.map((variant, index) => (\n                  <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                    {variant}\n                  </Badge>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AATA;;;;;;;;;;AAWO,SAAS;IACd,MAAM,EACJ,WAAW,EACX,SAAS,EACT,UAAU,EACV,aAAa,EACb,WAAW,EACX,iBAAiB,EACjB,eAAe,EACf,gBAAgB,EAChB,mBAAmB,EACnB,iBAAiB,EACjB,eAAe,EAChB,GAAG,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD;IAEnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,MAAM,aAAa,CAAC,OAAe;QACjC,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,aAAa,CAAC;QAClB,OAAQ;YACN,KAAK;gBACH,gBAAgB;gBAChB;YACF,KAAK;gBACH,iBAAiB;oBAAE,SAAS;gBAAU;gBACtC;YACF,KAAK;gBACH,iBAAiB;oBAAE,WAAW;gBAAU;gBACxC;YACF,KAAK;gBACH,kBAAkB;oBAAE,MAAM;gBAAU;gBACpC;YACF,KAAK;gBACH,kBAAkB;oBAAE,OAAO;gBAAU;gBACrC;YACF,KAAK;gBACH,kBAAkB;oBAAE,aAAa;gBAAU;gBAC3C;QACJ;QACA,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,gBAAgB,CAAC,EACrB,KAAK,EACL,KAAK,EACL,KAAK,EACL,MAAM,IAAI,EAMX,iBACC,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;;gCAAqC;gCAAM;;;;;;;;;;;;;8BAE7D,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,sBAChB;;0CACE,8OAAC,iIAAA,CAAA,QAAK;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC5C,WAAU;gCACV,SAAS;;;;;;0CAEX,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAQ;gCAAQ,SAAS,IAAM,WAAW;0CAC1D,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,SAAQ;gCAAQ,SAAS;0CACzC,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;qDAIjB;;0CACE,8OAAC;gCAAK,WAAU;0CAAyB;;;;;;0CACzC,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS,IAAM,WAAW,OAAO;gCACjC,WAAU;0CAEV,cAAA,8OAAC,0MAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;IAQ7B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAGb,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC5C,8OAAC;oCAAc,WAAU;;sDACvB,8OAAC;4CAAK,WAAU;sDAAoC;;;;;;sDACpD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB;oDAAM;;;;;;8DAElC,8OAAC,iIAAA,CAAA,QAAK;oDACJ,OAAO;oDACP,UAAU,CAAC,IAAM,kBAAkB;4DAAE,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;wDAAC;oDAC3D,WAAU;;;;;;;;;;;;;mCAVN;;;;;;;;;;;;;;;;8BAmBhB,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCACC,OAAM;oCACN,OAAO;oCACP,OAAM;oCACN,MAAM,0MAAA,CAAA,WAAQ;;;;;;8CAEhB,8OAAC;oCACC,OAAM;oCACN,OAAO,WAAW,OAAO;oCACzB,OAAM;oCACN,MAAM,kMAAA,CAAA,OAAI;;;;;;8CAEZ,8OAAC;oCACC,OAAM;oCACN,OAAO,WAAW,SAAS;oCAC3B,OAAM;oCACN,MAAM,kMAAA,CAAA,OAAI;;;;;;;;;;;;;;;;;;8BAMhB,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,KAAK,QAAQ,iBAChD,8OAAC;oCAAc,WAAU;;sDACvB,8OAAC;4CAAK,WAAU;sDACb,IAAI,OAAO,CAAC,YAAY,OAAO,OAAO,CAAC,MAAM,CAAA,MAAO,IAAI,WAAW;;;;;;sDAEtE,8OAAC,iIAAA,CAAA,QAAK;4CACJ,SAAS,UAAU,YAAY;4CAC/B,WAAU;4CACV,SAAS,IAAM,oBAAoB;oDAAE,CAAC,IAAI,EAAE,CAAC;gDAAQ;sDAEpD,UAAU,OAAO;;;;;;;mCATZ;;;;;;;;;;;;;;;;8BAiBhB,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;sCAGV,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC;oCACC,OAAM;oCACN,OAAO,YAAY,IAAI;oCACvB,OAAM;oCACN,MAAM,wMAAA,CAAA,UAAO;;;;;;8CAEf,8OAAC;oCACC,OAAM;oCACN,OAAO,YAAY,KAAK;oCACxB,OAAM;oCACN,MAAM,wMAAA,CAAA,UAAO;;;;;;8CAEf,8OAAC;oCACC,OAAM;oCACN,OAAO,YAAY,WAAW;oCAC9B,OAAM;oCACN,MAAM,wMAAA,CAAA,UAAO;;;;;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAU;sDAAwB;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACZ,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAClC,8OAAC,iIAAA,CAAA,QAAK;oDAAa,SAAQ;oDAAU,WAAU;8DAC5C;mDADS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9B", "debugId": null}}, {"offset": {"line": 1814, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/components/VisualPreviewSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useMemo } from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport { useBrandingStore } from '@/store/brandingStore';\nimport { ShoppingCart, Star, Heart } from 'lucide-react';\n\nexport function VisualPreviewSection() {\n  const {\n    businessInput,\n    brandColors,\n    brandFonts,\n    graphicAssets,\n    productInfo\n  } = useBrandingStore();\n\n  // Generate dynamic CSS styles based on state\n  const dynamicStyles = useMemo(() => {\n    const baseStyles = {\n      primaryColor: brandColors.primary,\n      secondaryColor: brandColors.secondary,\n      accentColor: brandColors.accent,\n      backgroundColor: brandColors.background,\n      textColor: brandColors.text,\n      primaryFont: brandFonts.primary,\n      secondaryFont: brandFonts.secondary,\n    };\n\n    const gradientStyle = graphicAssets.useGradients\n      ? `linear-gradient(135deg, ${brandColors.primary}, ${brandColors.secondary})`\n      : brandColors.primary;\n\n    const glowEffect = graphicAssets.useGlowEffects\n      ? `0 0 20px ${brandColors.primary}40`\n      : 'none';\n\n    const borderStyle = graphicAssets.useThickBorders\n      ? `3px solid ${brandColors.accent}`\n      : `1px solid ${brandColors.primary}20`;\n\n    const glassmorphismStyle = graphicAssets.useGlassmorphism\n      ? {\n          background: `${brandColors.background}80`,\n          backdropFilter: 'blur(10px)',\n          border: `1px solid ${brandColors.primary}20`,\n        }\n      : {};\n\n    return {\n      ...baseStyles,\n      gradientStyle,\n      glowEffect,\n      borderStyle,\n      glassmorphismStyle,\n    };\n  }, [brandColors, brandFonts, graphicAssets]);\n\n  const TypographyCard = () => (\n    <Card \n      className=\"mb-6 overflow-hidden transition-all duration-300\"\n      style={{\n        background: dynamicStyles.gradientStyle,\n        boxShadow: dynamicStyles.glowEffect,\n        border: dynamicStyles.borderStyle,\n        ...dynamicStyles.glassmorphismStyle,\n      }}\n    >\n      <CardContent className=\"p-8 text-center\">\n        <h1 \n          className=\"text-4xl font-bold mb-4 transition-all duration-300\"\n          style={{\n            fontFamily: dynamicStyles.primaryFont,\n            color: brandColors.background,\n            textShadow: graphicAssets.useGlowEffects ? `0 0 10px ${brandColors.background}50` : 'none',\n          }}\n        >\n          {businessInput.name || 'Your Business Name'}\n        </h1>\n        <p \n          className=\"text-lg opacity-90 transition-all duration-300\"\n          style={{\n            fontFamily: dynamicStyles.secondaryFont,\n            color: brandColors.background,\n          }}\n        >\n          {businessInput.description || 'Your business description will appear here'}\n        </p>\n      </CardContent>\n    </Card>\n  );\n\n  const WebsiteCard = () => (\n    <Card \n      className=\"overflow-hidden transition-all duration-300\"\n      style={{\n        border: dynamicStyles.borderStyle,\n        ...dynamicStyles.glassmorphismStyle,\n      }}\n    >\n      <CardContent className=\"p-0\">\n        {/* Mock Website Header */}\n        <div \n          className=\"p-4 transition-all duration-300\"\n          style={{\n            background: graphicAssets.useGradients \n              ? `linear-gradient(90deg, ${brandColors.primary}, ${brandColors.secondary})`\n              : brandColors.primary,\n          }}\n        >\n          <div className=\"flex items-center justify-between\">\n            <h2 \n              className=\"text-xl font-bold transition-all duration-300\"\n              style={{\n                fontFamily: dynamicStyles.primaryFont,\n                color: brandColors.background,\n              }}\n            >\n              {businessInput.name || 'Business'}\n            </h2>\n            <div className=\"flex space-x-4\">\n              <span \n                className=\"text-sm transition-all duration-300\"\n                style={{\n                  fontFamily: dynamicStyles.secondaryFont,\n                  color: brandColors.background,\n                }}\n              >\n                Home\n              </span>\n              <span \n                className=\"text-sm transition-all duration-300\"\n                style={{\n                  fontFamily: dynamicStyles.secondaryFont,\n                  color: brandColors.background,\n                }}\n              >\n                Products\n              </span>\n              <span \n                className=\"text-sm transition-all duration-300\"\n                style={{\n                  fontFamily: dynamicStyles.secondaryFont,\n                  color: brandColors.background,\n                }}\n              >\n                Contact\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Mock Product Card */}\n        <div className=\"p-6\">\n          <div \n            className=\"rounded-lg p-6 transition-all duration-300\"\n            style={{\n              background: graphicAssets.useGlassmorphism \n                ? `${brandColors.background}80`\n                : brandColors.background,\n              border: `1px solid ${brandColors.primary}20`,\n              backdropFilter: graphicAssets.useGlassmorphism ? 'blur(10px)' : 'none',\n              boxShadow: graphicAssets.useGlowEffects \n                ? `0 4px 20px ${brandColors.primary}20`\n                : '0 2px 10px rgba(0,0,0,0.1)',\n            }}\n          >\n            {/* Product Image Placeholder */}\n            <div \n              className=\"w-full h-32 rounded-lg mb-4 transition-all duration-300\"\n              style={{\n                background: graphicAssets.useGradients\n                  ? `linear-gradient(45deg, ${brandColors.secondary}, ${brandColors.accent})`\n                  : brandColors.secondary,\n              }}\n            />\n\n            {/* Product Info */}\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <h3 \n                  className=\"text-lg font-semibold transition-all duration-300\"\n                  style={{\n                    fontFamily: dynamicStyles.primaryFont,\n                    color: brandColors.text,\n                  }}\n                >\n                  {productInfo.name}\n                </h3>\n                <div className=\"flex items-center space-x-1\">\n                  {[...Array(5)].map((_, i) => (\n                    <Star \n                      key={i} \n                      className=\"h-4 w-4 fill-current transition-all duration-300\"\n                      style={{ color: brandColors.accent }}\n                    />\n                  ))}\n                </div>\n              </div>\n\n              <p \n                className=\"text-sm transition-all duration-300\"\n                style={{\n                  fontFamily: dynamicStyles.secondaryFont,\n                  color: `${brandColors.text}80`,\n                }}\n              >\n                {productInfo.description}\n              </p>\n\n              {/* Variants */}\n              <div className=\"flex flex-wrap gap-2\">\n                {productInfo.variants.map((variant, index) => (\n                  <Badge \n                    key={index}\n                    variant=\"outline\"\n                    className=\"transition-all duration-300\"\n                    style={{\n                      borderColor: brandColors.primary,\n                      color: brandColors.primary,\n                    }}\n                  >\n                    {variant}\n                  </Badge>\n                ))}\n              </div>\n\n              {/* Price and Actions */}\n              <div className=\"flex items-center justify-between pt-2\">\n                <span \n                  className=\"text-2xl font-bold transition-all duration-300\"\n                  style={{\n                    fontFamily: dynamicStyles.primaryFont,\n                    color: brandColors.primary,\n                  }}\n                >\n                  {productInfo.price}\n                </span>\n                <div className=\"flex space-x-2\">\n                  <Button \n                    size=\"sm\" \n                    variant=\"outline\"\n                    className=\"transition-all duration-300\"\n                    style={{\n                      borderColor: brandColors.primary,\n                      color: brandColors.primary,\n                    }}\n                  >\n                    <Heart className=\"h-4 w-4\" />\n                  </Button>\n                  <Button \n                    size=\"sm\"\n                    className=\"transition-all duration-300\"\n                    style={{\n                      background: graphicAssets.useGradients\n                        ? `linear-gradient(135deg, ${brandColors.primary}, ${brandColors.secondary})`\n                        : brandColors.primary,\n                      color: brandColors.background,\n                      boxShadow: graphicAssets.useGlowEffects \n                        ? `0 0 10px ${brandColors.primary}40`\n                        : 'none',\n                    }}\n                  >\n                    <ShoppingCart className=\"h-4 w-4 mr-2\" />\n                    Add to Cart\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n\n  return (\n    <div className=\"h-full p-6 overflow-y-auto\">\n      <div className=\"max-w-2xl mx-auto\">\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n          Visual Brand Preview\n        </h2>\n        \n        <TypographyCard />\n        <WebsiteCard />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAPA;;;;;;;;AASO,SAAS;IACd,MAAM,EACJ,aAAa,EACb,WAAW,EACX,UAAU,EACV,aAAa,EACb,WAAW,EACZ,GAAG,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD;IAEnB,6CAA6C;IAC7C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,MAAM,aAAa;YACjB,cAAc,YAAY,OAAO;YACjC,gBAAgB,YAAY,SAAS;YACrC,aAAa,YAAY,MAAM;YAC/B,iBAAiB,YAAY,UAAU;YACvC,WAAW,YAAY,IAAI;YAC3B,aAAa,WAAW,OAAO;YAC/B,eAAe,WAAW,SAAS;QACrC;QAEA,MAAM,gBAAgB,cAAc,YAAY,GAC5C,CAAC,wBAAwB,EAAE,YAAY,OAAO,CAAC,EAAE,EAAE,YAAY,SAAS,CAAC,CAAC,CAAC,GAC3E,YAAY,OAAO;QAEvB,MAAM,aAAa,cAAc,cAAc,GAC3C,CAAC,SAAS,EAAE,YAAY,OAAO,CAAC,EAAE,CAAC,GACnC;QAEJ,MAAM,cAAc,cAAc,eAAe,GAC7C,CAAC,UAAU,EAAE,YAAY,MAAM,EAAE,GACjC,CAAC,UAAU,EAAE,YAAY,OAAO,CAAC,EAAE,CAAC;QAExC,MAAM,qBAAqB,cAAc,gBAAgB,GACrD;YACE,YAAY,GAAG,YAAY,UAAU,CAAC,EAAE,CAAC;YACzC,gBAAgB;YAChB,QAAQ,CAAC,UAAU,EAAE,YAAY,OAAO,CAAC,EAAE,CAAC;QAC9C,IACA,CAAC;QAEL,OAAO;YACL,GAAG,UAAU;YACb;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAa;QAAY;KAAc;IAE3C,MAAM,iBAAiB,kBACrB,8OAAC,gIAAA,CAAA,OAAI;YACH,WAAU;YACV,OAAO;gBACL,YAAY,cAAc,aAAa;gBACvC,WAAW,cAAc,UAAU;gBACnC,QAAQ,cAAc,WAAW;gBACjC,GAAG,cAAc,kBAAkB;YACrC;sBAEA,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY,cAAc,WAAW;4BACrC,OAAO,YAAY,UAAU;4BAC7B,YAAY,cAAc,cAAc,GAAG,CAAC,SAAS,EAAE,YAAY,UAAU,CAAC,EAAE,CAAC,GAAG;wBACtF;kCAEC,cAAc,IAAI,IAAI;;;;;;kCAEzB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY,cAAc,aAAa;4BACvC,OAAO,YAAY,UAAU;wBAC/B;kCAEC,cAAc,WAAW,IAAI;;;;;;;;;;;;;;;;;IAMtC,MAAM,cAAc,kBAClB,8OAAC,gIAAA,CAAA,OAAI;YACH,WAAU;YACV,OAAO;gBACL,QAAQ,cAAc,WAAW;gBACjC,GAAG,cAAc,kBAAkB;YACrC;sBAEA,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY,cAAc,YAAY,GAClC,CAAC,uBAAuB,EAAE,YAAY,OAAO,CAAC,EAAE,EAAE,YAAY,SAAS,CAAC,CAAC,CAAC,GAC1E,YAAY,OAAO;wBACzB;kCAEA,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,YAAY,cAAc,WAAW;wCACrC,OAAO,YAAY,UAAU;oCAC/B;8CAEC,cAAc,IAAI,IAAI;;;;;;8CAEzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDACL,YAAY,cAAc,aAAa;gDACvC,OAAO,YAAY,UAAU;4CAC/B;sDACD;;;;;;sDAGD,8OAAC;4CACC,WAAU;4CACV,OAAO;gDACL,YAAY,cAAc,aAAa;gDACvC,OAAO,YAAY,UAAU;4CAC/B;sDACD;;;;;;sDAGD,8OAAC;4CACC,WAAU;4CACV,OAAO;gDACL,YAAY,cAAc,aAAa;gDACvC,OAAO,YAAY,UAAU;4CAC/B;sDACD;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY,cAAc,gBAAgB,GACtC,GAAG,YAAY,UAAU,CAAC,EAAE,CAAC,GAC7B,YAAY,UAAU;gCAC1B,QAAQ,CAAC,UAAU,EAAE,YAAY,OAAO,CAAC,EAAE,CAAC;gCAC5C,gBAAgB,cAAc,gBAAgB,GAAG,eAAe;gCAChE,WAAW,cAAc,cAAc,GACnC,CAAC,WAAW,EAAE,YAAY,OAAO,CAAC,EAAE,CAAC,GACrC;4BACN;;8CAGA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,YAAY,cAAc,YAAY,GAClC,CAAC,uBAAuB,EAAE,YAAY,SAAS,CAAC,EAAE,EAAE,YAAY,MAAM,CAAC,CAAC,CAAC,GACzE,YAAY,SAAS;oCAC3B;;;;;;8CAIF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDACL,YAAY,cAAc,WAAW;wDACrC,OAAO,YAAY,IAAI;oDACzB;8DAEC,YAAY,IAAI;;;;;;8DAEnB,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;4DAEH,WAAU;4DACV,OAAO;gEAAE,OAAO,YAAY,MAAM;4DAAC;2DAF9B;;;;;;;;;;;;;;;;sDAQb,8OAAC;4CACC,WAAU;4CACV,OAAO;gDACL,YAAY,cAAc,aAAa;gDACvC,OAAO,GAAG,YAAY,IAAI,CAAC,EAAE,CAAC;4CAChC;sDAEC,YAAY,WAAW;;;;;;sDAI1B,8OAAC;4CAAI,WAAU;sDACZ,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAClC,8OAAC,iIAAA,CAAA,QAAK;oDAEJ,SAAQ;oDACR,WAAU;oDACV,OAAO;wDACL,aAAa,YAAY,OAAO;wDAChC,OAAO,YAAY,OAAO;oDAC5B;8DAEC;mDARI;;;;;;;;;;sDAcX,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,OAAO;wDACL,YAAY,cAAc,WAAW;wDACrC,OAAO,YAAY,OAAO;oDAC5B;8DAEC,YAAY,KAAK;;;;;;8DAEpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,WAAU;4DACV,OAAO;gEACL,aAAa,YAAY,OAAO;gEAChC,OAAO,YAAY,OAAO;4DAC5B;sEAEA,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,WAAU;4DACV,OAAO;gEACL,YAAY,cAAc,YAAY,GAClC,CAAC,wBAAwB,EAAE,YAAY,OAAO,CAAC,EAAE,EAAE,YAAY,SAAS,CAAC,CAAC,CAAC,GAC3E,YAAY,OAAO;gEACvB,OAAO,YAAY,UAAU;gEAC7B,WAAW,cAAc,cAAc,GACnC,CAAC,SAAS,EAAE,YAAY,OAAO,CAAC,EAAE,CAAC,GACnC;4DACN;;8EAEA,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAY3D,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAItD,8OAAC;;;;;8BACD,8OAAC;;;;;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 2237, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/dukanify/small%20apps/branding-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { InputSection } from '@/components/InputSection';\nimport { BrandingDataSection } from '@/components/BrandingDataSection';\nimport { VisualPreviewSection } from '@/components/VisualPreviewSection';\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Top Section - Branding Data */}\n      <div className=\"w-full border-b bg-white\">\n        <BrandingDataSection />\n      </div>\n\n      {/* Main Content - Left and Right Sections */}\n      <div className=\"flex h-[calc(100vh-200px)]\">\n        {/* Left Section - Input */}\n        <div className=\"w-1/3 border-r bg-white\">\n          <InputSection />\n        </div>\n\n        {/* Right Section - Visual Preview */}\n        <div className=\"flex-1 bg-gray-50\">\n          <VisualPreviewSection />\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,yIAAA,CAAA,sBAAmB;;;;;;;;;;0BAItB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,eAAY;;;;;;;;;;kCAIf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0IAAA,CAAA,uBAAoB;;;;;;;;;;;;;;;;;;;;;;AAK/B", "debugId": null}}]}