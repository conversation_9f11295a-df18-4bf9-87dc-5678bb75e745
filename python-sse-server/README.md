# Flask Server-Sent Events (SSE) Server

A Flask-based Server-Sent Events server that streams JSON data from `brand.json` to connected clients with real-time file watching and automatic updates.

## Features

- 🔄 **Real-time streaming** of brand data via Server-Sent Events
- 📁 **File watching** - automatically detects changes to `brand.json` and broadcasts updates
- 🌐 **CORS enabled** for localhost:3000 (Next.js integration)
- 🔌 **Multiple client support** - handles concurrent SSE connections
- 🔄 **Auto-reconnection** - robust client-side reconnection logic
- 💓 **Keep-alive** - maintains connections with periodic pings
- 🛡️ **Error handling** - comprehensive error handling and recovery

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Start the Flask Server

```bash
python app.py
```

The server will start on `http://localhost:5001` with the following endpoints:

- `/` - Server information and status
- `/events` - SSE endpoint for real-time data streaming
- `/current` - Get current brand data as JSON
- `/health` - Health check endpoint
- `/trigger-update` - Manually trigger an update (POST)

### 3. Test the Connection

Open your browser and navigate to:
- `http://localhost:5001` - Server info
- `http://localhost:5001/events` - SSE stream (you'll see the raw event stream)

## Client Integration

### Next.js React Hook

Use the provided React hook for easy integration:

```javascript
import { useBrandSSE } from './path/to/client-examples';

function MyComponent() {
  const { brandData, connectionStatus, error, lastUpdated } = useBrandSSE();
  
  if (error) {
    return <div>Error: {error}</div>;
  }
  
  if (!brandData) {
    return <div>Loading...</div>;
  }
  
  return (
    <div>
      <h2>Brand Colors</h2>
      {Object.entries(brandData.brandColors || {}).map(([key, color]) => (
        <div key={key} style={{ backgroundColor: color }}>
          {key}: {color}
        </div>
      ))}
    </div>
  );
}
```

### Vanilla JavaScript

```javascript
const eventSource = new EventSource('http://localhost:5001/events');

eventSource.onmessage = function(event) {
  if (event.data === 'ping') return; // Ignore keep-alive pings
  
  const brandData = JSON.parse(event.data);
  console.log('Received brand data:', brandData);
  
  // Update your UI here
  updateBrandDisplay(brandData);
};

eventSource.onerror = function(event) {
  console.error('SSE connection error:', event);
};
```

## File Structure

```
python-sse-server/
├── app.py                 # Main Flask SSE server
├── brand.json            # Brand data file (watched for changes)
├── requirements.txt      # Python dependencies
├── client-examples.js    # Complete client integration examples
├── README.md            # This documentation
└── main.py              # Original sample file
```

## API Endpoints

### GET `/`
Returns server information and current status.

**Response:**
```json
{
  "service": "Flask SSE Server",
  "version": "1.0.0",
  "endpoints": {...},
  "connected_clients": 2
}
```

### GET `/events`
Server-Sent Events endpoint. Streams brand data in real-time.

**Headers:**
- `Content-Type: text/event-stream`
- `Cache-Control: no-cache`
- `Connection: keep-alive`

**Event Format:**
```
id: 1699123456789
event: brand-update
data: {"brandColors": {...}, "productInfo": {...}}

```

### GET `/current`
Returns the current brand data as JSON.

**Response:**
```json
{
  "brandColors": {
    "primary": "#4f8ef7",
    "secondary": "#ff9800"
  },
  "productInfo": {
    "name": "Smart LED Lamp",
    "price": "₹1499"
  }
}
```

### GET `/health`
Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "connected_clients": 2,
  "brand_file_exists": true
}
```

### POST `/trigger-update`
Manually trigger a data update (useful for testing).

**Response:**
```json
{
  "message": "Update triggered",
  "clients_notified": 2
}
```

## Testing the Integration

### 1. Test File Watching

1. Start the Flask server: `python app.py`
2. Connect a client to `http://localhost:5001/events`
3. Modify `brand.json` and save the file
4. Observe the automatic update in the client

### 2. Test with curl

```bash
# Stream events
curl -N http://localhost:5001/events

# Get current data
curl http://localhost:5001/current

# Trigger manual update
curl -X POST http://localhost:5001/trigger-update
```

### 3. Test with Browser

Open the browser developer console and run:

```javascript
const eventSource = new EventSource('http://localhost:5001/events');
eventSource.onmessage = (e) => console.log('Data:', JSON.parse(e.data));
eventSource.onerror = (e) => console.error('Error:', e);
```

## Configuration

### CORS Settings

The server is configured to allow connections from:
- `http://localhost:3000`
- `http://127.0.0.1:3000`

To add more origins, modify the CORS configuration in `app.py`:

```python
CORS(app, origins=["http://localhost:3000", "http://your-domain.com"])
```

### Server Settings

Default server configuration:
- **Host:** `0.0.0.0` (all interfaces)
- **Port:** `5001`
- **Debug:** `False` (required for SSE)
- **Threading:** `True` (required for multiple clients)

## Troubleshooting

### Common Issues

1. **Connection Refused**
   - Ensure the Flask server is running
   - Check if port 5001 is available
   - Verify firewall settings

2. **CORS Errors**
   - Ensure your client origin is in the CORS configuration
   - Check browser console for specific CORS errors

3. **File Changes Not Detected**
   - Verify `brand.json` exists in the same directory as `app.py`
   - Check file permissions
   - Some editors create temporary files - try direct file editing

4. **Client Disconnections**
   - Check network stability
   - Verify the client implements proper error handling
   - Monitor server logs for connection errors

### Debug Mode

For debugging, you can enable verbose logging by modifying `app.py`:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Performance Considerations

- The server can handle multiple concurrent connections
- File watching has minimal performance impact
- Consider implementing rate limiting for production use
- Monitor memory usage with many long-lived connections

## Production Deployment

For production deployment, consider:

1. **Use a production WSGI server** (e.g., Gunicorn, uWSGI)
2. **Configure proper CORS origins** for your domain
3. **Add authentication/authorization** if needed
4. **Implement rate limiting** to prevent abuse
5. **Use HTTPS** for secure connections
6. **Monitor server resources** and connection counts

Example Gunicorn deployment:

```bash
pip install gunicorn
gunicorn --worker-class gevent --workers 1 --bind 0.0.0.0:5001 app:app
```

## License

This project is provided as-is for educational and development purposes.
