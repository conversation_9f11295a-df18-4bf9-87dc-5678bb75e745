#!/usr/bin/env python3
"""
Flask Server-Sent Events (SSE) Server
Streams brand.json data to connected clients and watches for file changes.
"""

import json
import os
import time
import threading
from queue import Queue
from typing import Dict, Any, Set
from flask import Flask, Response, request, jsonify
from flask_cors import CORS
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

app = Flask(__name__)

# Configure CORS to allow connections from localhost:3000
CORS(app, origins=["http://localhost:3000", "http://127.0.0.1:3000"])

# Global variables for managing SSE connections
clients: Set[Queue] = set()
current_data: Dict[Any, Any] = {}
data_lock = threading.Lock()

# File path for brand.json
BRAND_JSON_PATH = "brand.json"


class BrandFileHandler(FileSystemEventHandler):
    """Handler for file system events on brand.json"""
    
    def on_modified(self, event):
        if not event.is_directory and event.src_path.endswith('brand.json'):
            print(f"Detected change in {event.src_path}")
            load_and_broadcast_data()


def load_brand_data() -> Dict[Any, Any]:
    """Load brand data from JSON file"""
    try:
        with open(BRAND_JSON_PATH, 'r', encoding='utf-8') as file:
            data = json.load(file)
            print(f"Loaded brand data: {len(str(data))} characters")
            return data
    except FileNotFoundError:
        print(f"Warning: {BRAND_JSON_PATH} not found")
        return {"error": "brand.json file not found"}
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        return {"error": f"Invalid JSON format: {str(e)}"}
    except Exception as e:
        print(f"Error loading brand data: {e}")
        return {"error": f"Failed to load data: {str(e)}"}


def load_and_broadcast_data():
    """Load data from file and broadcast to all connected clients"""
    global current_data
    
    new_data = load_brand_data()
    
    with data_lock:
        current_data = new_data
        
    # Broadcast to all connected clients
    broadcast_to_clients(new_data)


def broadcast_to_clients(data: Dict[Any, Any]):
    """Send data to all connected SSE clients"""
    if not clients:
        return
        
    message = format_sse_message(data)
    disconnected_clients = set()
    
    for client_queue in clients.copy():
        try:
            client_queue.put(message)
        except Exception as e:
            print(f"Error sending to client: {e}")
            disconnected_clients.add(client_queue)
    
    # Remove disconnected clients
    for client in disconnected_clients:
        clients.discard(client)
    
    print(f"Broadcasted to {len(clients)} clients")


def format_sse_message(data: Dict[Any, Any], event_type: str = "brand-update") -> str:
    """Format data as SSE message"""
    timestamp = int(time.time() * 1000)  # milliseconds
    json_data = json.dumps(data)
    
    return f"id: {timestamp}\nevent: {event_type}\ndata: {json_data}\n\n"


def setup_file_watcher():
    """Set up file system watcher for brand.json"""
    event_handler = BrandFileHandler()
    observer = Observer()
    
    # Watch the current directory for changes to brand.json
    observer.schedule(event_handler, path='.', recursive=False)
    observer.start()
    
    print("File watcher started for brand.json")
    return observer


@app.route('/')
def index():
    """Basic info endpoint"""
    return jsonify({
        "service": "Flask SSE Server",
        "version": "1.0.0",
        "endpoints": {
            "/events": "Server-Sent Events stream for brand data",
            "/current": "Get current brand data as JSON",
            "/health": "Health check endpoint"
        },
        "connected_clients": len(clients)
    })


@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "connected_clients": len(clients),
        "brand_file_exists": os.path.exists(BRAND_JSON_PATH)
    })


@app.route('/current')
def get_current_data():
    """Get current brand data as JSON"""
    with data_lock:
        return jsonify(current_data)


@app.route('/events')
def events():
    """Server-Sent Events endpoint"""
    def event_stream():
        # Create a queue for this client
        client_queue = Queue()
        clients.add(client_queue)
        
        try:
            # Send current data immediately upon connection
            with data_lock:
                if current_data:
                    initial_message = format_sse_message(current_data, "initial-data")
                    yield initial_message
            
            # Send keep-alive and data updates
            while True:
                try:
                    # Wait for new data or timeout for keep-alive
                    message = client_queue.get(timeout=30)
                    yield message
                except:
                    # Send keep-alive ping
                    yield "data: ping\n\n"
                    
        except GeneratorExit:
            # Client disconnected
            pass
        finally:
            # Clean up when client disconnects
            clients.discard(client_queue)
            print(f"Client disconnected. Remaining clients: {len(clients)}")
    
    response = Response(
        event_stream(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        }
    )
    
    print(f"New SSE client connected. Total clients: {len(clients) + 1}")
    return response


@app.route('/trigger-update', methods=['POST'])
def trigger_update():
    """Manually trigger a data update (for testing)"""
    load_and_broadcast_data()
    return jsonify({
        "message": "Update triggered",
        "clients_notified": len(clients)
    })


if __name__ == '__main__':
    print("Starting Flask SSE Server...")
    
    # Load initial data
    current_data = load_brand_data()
    
    # Set up file watcher
    observer = setup_file_watcher()
    
    try:
        print("Server starting on http://localhost:5001")
        print("SSE endpoint available at http://localhost:5001/events")
        print("Press Ctrl+C to stop the server")
        
        # Start Flask app
        app.run(
            host='0.0.0.0',
            port=5001,
            debug=False,  # Disable debug mode for SSE
            threaded=True,
            use_reloader=False  # Disable reloader to prevent file watcher conflicts
        )
    except KeyboardInterrupt:
        print("\nShutting down server...")
    finally:
        observer.stop()
        observer.join()
        print("Server stopped.")
