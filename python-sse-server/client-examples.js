/**
 * Next.js Client Examples for Flask SSE Server
 * Complete implementation with error handling and reconnection logic
 */

// ============================================================================
// 1. React Hook for SSE Connection
// ============================================================================

import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * Custom React hook for managing SSE connection to Flask server
 */
export function useBrandSSE(serverUrl = 'http://localhost:5001') {
  const [brandData, setBrandData] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  
  const eventSourceRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const baseReconnectDelay = 1000; // 1 second

  const connect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }

    setConnectionStatus('connecting');
    setError(null);

    try {
      const eventSource = new EventSource(`${serverUrl}/events`);
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('SSE connection opened');
        setConnectionStatus('connected');
        setError(null);
        reconnectAttempts.current = 0;
      };

      eventSource.onmessage = (event) => {
        try {
          if (event.data === 'ping') {
            // Keep-alive ping, ignore
            return;
          }
          
          const data = JSON.parse(event.data);
          setBrandData(data);
          setLastUpdated(new Date());
          console.log('Received brand data update:', data);
        } catch (err) {
          console.error('Error parsing SSE data:', err);
          setError('Failed to parse server data');
        }
      };

      // Handle custom event types
      eventSource.addEventListener('brand-update', (event) => {
        try {
          const data = JSON.parse(event.data);
          setBrandData(data);
          setLastUpdated(new Date());
          console.log('Brand data updated:', data);
        } catch (err) {
          console.error('Error parsing brand update:', err);
        }
      });

      eventSource.addEventListener('initial-data', (event) => {
        try {
          const data = JSON.parse(event.data);
          setBrandData(data);
          setLastUpdated(new Date());
          console.log('Initial brand data received:', data);
        } catch (err) {
          console.error('Error parsing initial data:', err);
        }
      });

      eventSource.onerror = (event) => {
        console.error('SSE connection error:', event);
        setConnectionStatus('error');
        
        if (eventSource.readyState === EventSource.CLOSED) {
          setConnectionStatus('disconnected');
          
          // Attempt reconnection with exponential backoff
          if (reconnectAttempts.current < maxReconnectAttempts) {
            const delay = baseReconnectDelay * Math.pow(2, reconnectAttempts.current);
            reconnectAttempts.current++;
            
            setError(`Connection lost. Reconnecting in ${delay/1000}s... (Attempt ${reconnectAttempts.current}/${maxReconnectAttempts})`);
            
            reconnectTimeoutRef.current = setTimeout(() => {
              connect();
            }, delay);
          } else {
            setError('Connection failed after maximum retry attempts. Please refresh the page.');
          }
        }
      };

    } catch (err) {
      console.error('Failed to create EventSource:', err);
      setConnectionStatus('error');
      setError('Failed to establish connection');
    }
  }, [serverUrl]);

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    setConnectionStatus('disconnected');
    reconnectAttempts.current = 0;
  }, []);

  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(connect, 100);
  }, [connect, disconnect]);

  useEffect(() => {
    connect();
    
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  return {
    brandData,
    connectionStatus,
    error,
    lastUpdated,
    reconnect,
    disconnect
  };
}

// ============================================================================
// 2. React Component Example
// ============================================================================

export function BrandDataDisplay() {
  const { 
    brandData, 
    connectionStatus, 
    error, 
    lastUpdated, 
    reconnect 
  } = useBrandSSE();

  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return 'text-green-600';
      case 'connecting': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      case 'disconnected': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected': return '🟢';
      case 'connecting': return '🟡';
      case 'error': return '🔴';
      case 'disconnected': return '⚫';
      default: return '⚫';
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* Connection Status */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span>{getStatusIcon(connectionStatus)}</span>
            <span className={`font-medium ${getStatusColor(connectionStatus)}`}>
              Status: {connectionStatus}
            </span>
          </div>
          
          {lastUpdated && (
            <span className="text-sm text-gray-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
        </div>
        
        {error && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
            {error}
            <button 
              onClick={reconnect}
              className="ml-2 px-2 py-1 bg-red-600 text-white rounded text-xs hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        )}
      </div>

      {/* Brand Data Display */}
      {brandData ? (
        <div className="space-y-6">
          {/* Brand Colors */}
          {brandData.brandColors && (
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-3">Brand Colors</h3>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                {Object.entries(brandData.brandColors).map(([key, color]) => (
                  <div key={key} className="text-center">
                    <div 
                      className="w-16 h-16 rounded-lg mx-auto mb-2 border"
                      style={{ backgroundColor: color }}
                    ></div>
                    <div className="text-sm font-medium">{key}</div>
                    <div className="text-xs text-gray-500">{color}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Product Info */}
          {brandData.productInfo && (
            <div className="bg-white p-4 rounded-lg shadow">
              <h3 className="text-lg font-semibold mb-3">Product Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <strong>Name:</strong> {brandData.productInfo.name}
                </div>
                <div>
                  <strong>Price:</strong> {brandData.productInfo.price}
                </div>
                <div className="md:col-span-2">
                  <strong>Description:</strong> {brandData.productInfo.description}
                </div>
                <div className="md:col-span-2">
                  <strong>Variants:</strong> {brandData.productInfo.variants?.join(', ')}
                </div>
              </div>
            </div>
          )}

          {/* Raw JSON Data */}
          <details className="bg-white p-4 rounded-lg shadow">
            <summary className="cursor-pointer font-semibold">Raw JSON Data</summary>
            <pre className="mt-3 p-3 bg-gray-100 rounded text-sm overflow-auto">
              {JSON.stringify(brandData, null, 2)}
            </pre>
          </details>
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          {connectionStatus === 'connecting' ? 'Connecting...' : 'No data available'}
        </div>
      )}
    </div>
  );
}

// ============================================================================
// 3. Vanilla JavaScript Example (for non-React projects)
// ============================================================================

class BrandSSEClient {
  constructor(serverUrl = 'http://localhost:5001') {
    this.serverUrl = serverUrl;
    this.eventSource = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.baseReconnectDelay = 1000;
    this.callbacks = {
      onData: [],
      onStatusChange: [],
      onError: []
    };
  }

  connect() {
    if (this.eventSource) {
      this.eventSource.close();
    }

    this.triggerStatusChange('connecting');

    try {
      this.eventSource = new EventSource(`${this.serverUrl}/events`);

      this.eventSource.onopen = () => {
        console.log('SSE connection opened');
        this.triggerStatusChange('connected');
        this.reconnectAttempts = 0;
      };

      this.eventSource.onmessage = (event) => {
        if (event.data === 'ping') return;
        
        try {
          const data = JSON.parse(event.data);
          this.triggerDataUpdate(data);
        } catch (err) {
          this.triggerError('Failed to parse server data');
        }
      };

      this.eventSource.addEventListener('brand-update', (event) => {
        try {
          const data = JSON.parse(event.data);
          this.triggerDataUpdate(data);
        } catch (err) {
          this.triggerError('Failed to parse brand update');
        }
      });

      this.eventSource.onerror = (event) => {
        console.error('SSE connection error:', event);
        this.triggerStatusChange('error');
        
        if (this.eventSource.readyState === EventSource.CLOSED) {
          this.triggerStatusChange('disconnected');
          this.attemptReconnect();
        }
      };

    } catch (err) {
      this.triggerStatusChange('error');
      this.triggerError('Failed to establish connection');
    }
  }

  disconnect() {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.triggerStatusChange('disconnected');
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      const delay = this.baseReconnectDelay * Math.pow(2, this.reconnectAttempts);
      this.reconnectAttempts++;
      
      setTimeout(() => {
        this.connect();
      }, delay);
    } else {
      this.triggerError('Connection failed after maximum retry attempts');
    }
  }

  onData(callback) {
    this.callbacks.onData.push(callback);
  }

  onStatusChange(callback) {
    this.callbacks.onStatusChange.push(callback);
  }

  onError(callback) {
    this.callbacks.onError.push(callback);
  }

  triggerDataUpdate(data) {
    this.callbacks.onData.forEach(callback => callback(data));
  }

  triggerStatusChange(status) {
    this.callbacks.onStatusChange.forEach(callback => callback(status));
  }

  triggerError(error) {
    this.callbacks.onError.forEach(callback => callback(error));
  }
}

// Usage example:
/*
const client = new BrandSSEClient();

client.onData((brandData) => {
  console.log('Received brand data:', brandData);
  // Update your UI here
});

client.onStatusChange((status) => {
  console.log('Connection status:', status);
  // Update connection indicator
});

client.onError((error) => {
  console.error('SSE Error:', error);
  // Show error message to user
});

client.connect();
*/
