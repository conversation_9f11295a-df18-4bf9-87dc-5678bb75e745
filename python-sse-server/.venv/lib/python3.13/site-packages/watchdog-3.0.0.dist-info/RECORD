../../../bin/watchmedo,sha256=VgrIk_7xDXbjYrObrs-4SGxSwxy54_v6GOclseKy96s,315
_watchdog_fsevents.cpython-313-darwin.so,sha256=MkaxyrtILvKo4Ta0OCaOZ0MZ2MVztx9jHnVTtpFiSaE,94104
watchdog-3.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
watchdog-3.0.0.dist-info/METADATA,sha256=wGpSQ8HDmvg192UHzC-WC5POSK_JPPsrQWThbREDgTA,36764
watchdog-3.0.0.dist-info/RECORD,,
watchdog-3.0.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
watchdog-3.0.0.dist-info/WHEEL,sha256=memlX0NSEQnmSMa3rcNWPnk4cttudwgAZx3qq8qO4ME,115
watchdog-3.0.0.dist-info/entry_points.txt,sha256=qt_Oe2U5Zlfz7LNA3PHipn3_1zlfRTp9dk3wTS3Ivb8,66
watchdog-3.0.0.dist-info/licenses/AUTHORS,sha256=gDi-g3FfUuViWOtmZ_QDsucGBISUxaC2OpsDGD9Evo0,2880
watchdog-3.0.0.dist-info/licenses/COPYING,sha256=Ash2D5iKdukqnWy1JUVqhvew_RlThw3Ukd5ZVcuXTUE,625
watchdog-3.0.0.dist-info/licenses/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
watchdog-3.0.0.dist-info/top_level.txt,sha256=JYP7SvqSWBmdMDcnBwaMd48Ngr9V0t8UbNvmAQ9LTdc,28
watchdog/__init__.py,sha256=FslnbeodNBt4aVEqkpKGrY0KqsG4hCkvsH-hDLujjQI,651
watchdog/__pycache__/__init__.cpython-313.pyc,,
watchdog/__pycache__/events.cpython-313.pyc,,
watchdog/__pycache__/version.cpython-313.pyc,,
watchdog/__pycache__/watchmedo.cpython-313.pyc,,
watchdog/events.py,sha256=p4gaGmmU2zB23u509TCJIiF11g1bqzdd2WDr1sy3HOM,16579
watchdog/observers/__init__.py,sha256=NhIRYdulInxCNYQisZt5Kam75YdJxvIHPnO0UG_w_Ic,3533
watchdog/observers/__pycache__/__init__.cpython-313.pyc,,
watchdog/observers/__pycache__/api.cpython-313.pyc,,
watchdog/observers/__pycache__/fsevents.cpython-313.pyc,,
watchdog/observers/__pycache__/fsevents2.cpython-313.pyc,,
watchdog/observers/__pycache__/inotify.cpython-313.pyc,,
watchdog/observers/__pycache__/inotify_buffer.cpython-313.pyc,,
watchdog/observers/__pycache__/inotify_c.cpython-313.pyc,,
watchdog/observers/__pycache__/kqueue.cpython-313.pyc,,
watchdog/observers/__pycache__/polling.cpython-313.pyc,,
watchdog/observers/__pycache__/read_directory_changes.cpython-313.pyc,,
watchdog/observers/__pycache__/winapi.cpython-313.pyc,,
watchdog/observers/api.py,sha256=VMAr8p3psEf7Ak9CpstwX89WOpGBdd_sMjg8XxQQgz4,12074
watchdog/observers/fsevents.py,sha256=TKCgERx4Jifx0lctGM9C6rsTij3KpixjeAzVbsST_nc,14100
watchdog/observers/fsevents2.py,sha256=O90zJg7fDzpelSvN7F_HmVJ97DGVyWwfVQ46CENfdD0,9594
watchdog/observers/inotify.py,sha256=6te5ZAo29SLrDsdHadXoii2I8htxtkHenhUaeEF2fC4,9543
watchdog/observers/inotify_buffer.py,sha256=Et5VcdDXS4FJ0KAdBZvMMucH4Dds0Ejvhbcy-WHsDTk,4747
watchdog/observers/inotify_c.py,sha256=l51sJMFVFYtPK-0dyZzKnbe7gBcv0epqjRgDE3zp0g8,19739
watchdog/observers/kqueue.py,sha256=br4btdKx-_8BeynkkofV3JnjBvYH04UtZ7Tc82_elKA,23950
watchdog/observers/polling.py,sha256=DvRlUzyq81pIjci3VOxY88gBWrmuyr_B6nxEKURGqkg,4810
watchdog/observers/read_directory_changes.py,sha256=2LoLtE0VFw-ammpHIRR66EvT3KlohNZ7BI5Cq40D4MA,5863
watchdog/observers/winapi.py,sha256=QR19laneSNfBw1Yr7XMIIuBS3pBWuzQ-iglBAAm4iPk,13682
watchdog/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
watchdog/tricks/__init__.py,sha256=lo1tsPwxjQ_aXyBb4w_H00krl8prIVXFJBo5AFBo2oE,9792
watchdog/tricks/__pycache__/__init__.cpython-313.pyc,,
watchdog/utils/__init__.py,sha256=rnaRJZoCPo1bxLD10ge36uEXzDapCGYB4IO0J4P4fKg,4498
watchdog/utils/__pycache__/__init__.cpython-313.pyc,,
watchdog/utils/__pycache__/bricks.cpython-313.pyc,,
watchdog/utils/__pycache__/delayed_queue.cpython-313.pyc,,
watchdog/utils/__pycache__/dirsnapshot.cpython-313.pyc,,
watchdog/utils/__pycache__/echo.cpython-313.pyc,,
watchdog/utils/__pycache__/event_debouncer.cpython-313.pyc,,
watchdog/utils/__pycache__/patterns.cpython-313.pyc,,
watchdog/utils/__pycache__/platform.cpython-313.pyc,,
watchdog/utils/__pycache__/process_watcher.cpython-313.pyc,,
watchdog/utils/bricks.py,sha256=auTxkTI2tvKTTwc5bH8HrZjPdjgx8gqHSksike30DSw,2885
watchdog/utils/delayed_queue.py,sha256=jhdsAWgc3d5JDLt2zZi-p98AIfn4DZ59l7iQdIfJ1YU,3073
watchdog/utils/dirsnapshot.py,sha256=_ZsFkAUJu1-tIZtbQVNeN1icnXoT7sckIewTgJQ5HrY,11829
watchdog/utils/echo.py,sha256=5Se1Wieh3oCycRADMr_P0toNzYRZfu0zCL2jDx7vnE0,5173
watchdog/utils/event_debouncer.py,sha256=Vc9bJMtysfl4ChQGdPWCnqzoCm0h_MR6AYcV-DfncRQ,1797
watchdog/utils/patterns.py,sha256=_m4Z89Rx2DGxHvft3CAfATDlkZI2RgWnDKCyELc1LKQ,3940
watchdog/utils/platform.py,sha256=qwQ4bM27Etv6Ytws-kbB5WcGO04MMWAJafNaEYQT5vQ,1515
watchdog/utils/process_watcher.py,sha256=qq2Dg3qbIU3Ul-UNlEnsVYcU9BGsXDXJxMDG7JRFNqg,715
watchdog/version.py,sha256=N5ovp8Y7CSPu5QG6Jrbx2Oq2oA5xvoe7nuYLmtOIo24,1002
watchdog/watchmedo.py,sha256=RO3QxjCgoVPGbbIu_9KgaEm7nksRFJVnTyPh5XFUsXY,25210
