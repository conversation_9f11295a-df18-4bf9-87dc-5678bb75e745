<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flask SSE Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.connecting { background-color: #fff3cd; color: #856404; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.disconnected { background-color: #e2e3e5; color: #383d41; }
        
        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .color-item {
            text-align: center;
        }
        
        .color-swatch {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            margin: 0 auto 8px;
            border: 2px solid #ddd;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .color-name {
            font-weight: 600;
            text-transform: capitalize;
        }
        
        .color-value {
            font-size: 12px;
            color: #666;
            font-family: monospace;
        }
        
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .product-item {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .product-label {
            font-weight: 600;
            color: #495057;
        }
        
        .product-value {
            margin-top: 4px;
        }
        
        .variants {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }
        
        .variant-tag {
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .json-viewer {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        button:hover {
            opacity: 0.9;
        }
        
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .timestamp {
            color: #888;
        }
    </style>
</head>
<body>
    <h1>🚀 Flask SSE Server Demo</h1>
    
    <div class="container">
        <div id="status" class="status disconnected">
            <span id="status-icon">⚫</span>
            <span id="status-text">Disconnected</span>
            <span id="last-updated"></span>
        </div>
        
        <div class="controls">
            <button id="connect-btn" class="btn-primary">Connect</button>
            <button id="disconnect-btn" class="btn-secondary">Disconnect</button>
            <button id="trigger-btn" class="btn-secondary">Trigger Update</button>
            <button id="clear-log-btn" class="btn-danger">Clear Log</button>
        </div>
    </div>
    
    <div class="container">
        <h2>🎨 Brand Colors</h2>
        <div id="colors" class="color-grid">
            <p>No color data available</p>
        </div>
    </div>
    
    <div class="container">
        <h2>📦 Product Information</h2>
        <div id="product" class="product-grid">
            <p>No product data available</p>
        </div>
    </div>
    
    <div class="container">
        <h2>📄 Raw JSON Data</h2>
        <div id="json-data" class="json-viewer">No data received yet</div>
    </div>
    
    <div class="container">
        <h2>📋 Event Log</h2>
        <div id="log" class="log"></div>
    </div>

    <script>
        class SSEDemo {
            constructor() {
                this.eventSource = null;
                this.serverUrl = 'http://localhost:5001';
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                
                this.initializeElements();
                this.attachEventListeners();
                this.log('Demo initialized');
            }
            
            initializeElements() {
                this.statusEl = document.getElementById('status');
                this.statusIconEl = document.getElementById('status-icon');
                this.statusTextEl = document.getElementById('status-text');
                this.lastUpdatedEl = document.getElementById('last-updated');
                this.colorsEl = document.getElementById('colors');
                this.productEl = document.getElementById('product');
                this.jsonDataEl = document.getElementById('json-data');
                this.logEl = document.getElementById('log');
                
                this.connectBtn = document.getElementById('connect-btn');
                this.disconnectBtn = document.getElementById('disconnect-btn');
                this.triggerBtn = document.getElementById('trigger-btn');
                this.clearLogBtn = document.getElementById('clear-log-btn');
            }
            
            attachEventListeners() {
                this.connectBtn.addEventListener('click', () => this.connect());
                this.disconnectBtn.addEventListener('click', () => this.disconnect());
                this.triggerBtn.addEventListener('click', () => this.triggerUpdate());
                this.clearLogBtn.addEventListener('click', () => this.clearLog());
            }
            
            connect() {
                if (this.eventSource) {
                    this.eventSource.close();
                }
                
                this.updateStatus('connecting', '🟡', 'Connecting...');
                this.log('Attempting to connect to SSE server...');
                
                try {
                    this.eventSource = new EventSource(`${this.serverUrl}/events`);
                    
                    this.eventSource.onopen = () => {
                        this.updateStatus('connected', '🟢', 'Connected');
                        this.log('✅ SSE connection established');
                        this.reconnectAttempts = 0;
                    };
                    
                    this.eventSource.onmessage = (event) => {
                        if (event.data === 'ping') {
                            this.log('💓 Keep-alive ping received');
                            return;
                        }
                        
                        try {
                            const data = JSON.parse(event.data);
                            this.handleDataUpdate(data, event.type || 'message');
                        } catch (err) {
                            this.log(`❌ Error parsing data: ${err.message}`);
                        }
                    };
                    
                    this.eventSource.addEventListener('brand-update', (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            this.handleDataUpdate(data, 'brand-update');
                        } catch (err) {
                            this.log(`❌ Error parsing brand update: ${err.message}`);
                        }
                    });
                    
                    this.eventSource.addEventListener('initial-data', (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            this.handleDataUpdate(data, 'initial-data');
                        } catch (err) {
                            this.log(`❌ Error parsing initial data: ${err.message}`);
                        }
                    });
                    
                    this.eventSource.onerror = (event) => {
                        this.log('❌ SSE connection error');
                        this.updateStatus('error', '🔴', 'Connection Error');
                        
                        if (this.eventSource.readyState === EventSource.CLOSED) {
                            this.updateStatus('disconnected', '⚫', 'Disconnected');
                            this.attemptReconnect();
                        }
                    };
                    
                } catch (err) {
                    this.log(`❌ Failed to create EventSource: ${err.message}`);
                    this.updateStatus('error', '🔴', 'Connection Failed');
                }
            }
            
            disconnect() {
                if (this.eventSource) {
                    this.eventSource.close();
                    this.eventSource = null;
                }
                this.updateStatus('disconnected', '⚫', 'Disconnected');
                this.log('🔌 Disconnected from SSE server');
            }
            
            attemptReconnect() {
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    const delay = 1000 * Math.pow(2, this.reconnectAttempts);
                    this.reconnectAttempts++;
                    
                    this.log(`🔄 Reconnecting in ${delay/1000}s... (Attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
                    
                    setTimeout(() => {
                        this.connect();
                    }, delay);
                } else {
                    this.log('❌ Max reconnection attempts reached');
                }
            }
            
            async triggerUpdate() {
                try {
                    const response = await fetch(`${this.serverUrl}/trigger-update`, {
                        method: 'POST'
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        this.log(`🔄 Manual update triggered. Clients notified: ${data.clients_notified}`);
                    } else {
                        this.log(`❌ Failed to trigger update: ${response.status}`);
                    }
                } catch (err) {
                    this.log(`❌ Error triggering update: ${err.message}`);
                }
            }
            
            handleDataUpdate(data, eventType) {
                this.log(`📦 Received ${eventType}: ${JSON.stringify(data).length} characters`);
                this.updateLastUpdated();
                this.updateUI(data);
            }
            
            updateStatus(status, icon, text) {
                this.statusEl.className = `status ${status}`;
                this.statusIconEl.textContent = icon;
                this.statusTextEl.textContent = text;
            }
            
            updateLastUpdated() {
                const now = new Date();
                this.lastUpdatedEl.textContent = `Last updated: ${now.toLocaleTimeString()}`;
            }
            
            updateUI(data) {
                this.updateColors(data.brandColors);
                this.updateProduct(data.productInfo);
                this.updateJsonData(data);
            }
            
            updateColors(colors) {
                if (!colors) {
                    this.colorsEl.innerHTML = '<p>No color data available</p>';
                    return;
                }
                
                const colorItems = Object.entries(colors).map(([name, color]) => `
                    <div class="color-item">
                        <div class="color-swatch" style="background-color: ${color}"></div>
                        <div class="color-name">${name}</div>
                        <div class="color-value">${color}</div>
                    </div>
                `).join('');
                
                this.colorsEl.innerHTML = colorItems;
            }
            
            updateProduct(product) {
                if (!product) {
                    this.productEl.innerHTML = '<p>No product data available</p>';
                    return;
                }
                
                const variants = product.variants ? 
                    product.variants.map(v => `<span class="variant-tag">${v}</span>`).join('') : '';
                
                this.productEl.innerHTML = `
                    <div class="product-item">
                        <div class="product-label">Name</div>
                        <div class="product-value">${product.name || 'N/A'}</div>
                    </div>
                    <div class="product-item">
                        <div class="product-label">Price</div>
                        <div class="product-value">${product.price || 'N/A'}</div>
                    </div>
                    <div class="product-item">
                        <div class="product-label">Description</div>
                        <div class="product-value">${product.description || 'N/A'}</div>
                    </div>
                    <div class="product-item">
                        <div class="product-label">Variants</div>
                        <div class="variants">${variants || 'None'}</div>
                    </div>
                `;
            }
            
            updateJsonData(data) {
                this.jsonDataEl.textContent = JSON.stringify(data, null, 2);
            }
            
            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
                this.logEl.appendChild(logEntry);
                this.logEl.scrollTop = this.logEl.scrollHeight;
            }
            
            clearLog() {
                this.logEl.innerHTML = '';
                this.log('Log cleared');
            }
        }
        
        // Initialize the demo when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.sseDemo = new SSEDemo();
        });
    </script>
</body>
</html>
