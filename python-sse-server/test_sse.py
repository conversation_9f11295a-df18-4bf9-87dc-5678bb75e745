#!/usr/bin/env python3
"""
Test script for the Flask SSE server
Tests basic functionality and file watching capabilities
"""

import requests
import json
import time
import threading
from urllib3.exceptions import InsecureRequestWarning
import urllib3

# Suppress SSL warnings for local testing
urllib3.disable_warnings(InsecureRequestWarning)

SERVER_URL = "http://localhost:5001"

def test_server_endpoints():
    """Test basic server endpoints"""
    print("🧪 Testing server endpoints...")
    
    try:
        # Test root endpoint
        response = requests.get(f"{SERVER_URL}/")
        if response.status_code == 200:
            print("✅ Root endpoint working")
            data = response.json()
            print(f"   Connected clients: {data.get('connected_clients', 0)}")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            
        # Test health endpoint
        response = requests.get(f"{SERVER_URL}/health")
        if response.status_code == 200:
            print("✅ Health endpoint working")
            health_data = response.json()
            print(f"   Status: {health_data.get('status')}")
            print(f"   Brand file exists: {health_data.get('brand_file_exists')}")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            
        # Test current data endpoint
        response = requests.get(f"{SERVER_URL}/current")
        if response.status_code == 200:
            print("✅ Current data endpoint working")
            current_data = response.json()
            if 'brandColors' in current_data:
                print(f"   Brand colors found: {len(current_data['brandColors'])} colors")
            if 'productInfo' in current_data:
                print(f"   Product: {current_data['productInfo'].get('name', 'Unknown')}")
        else:
            print(f"❌ Current data endpoint failed: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure the Flask server is running on port 5001")
        return False
    except Exception as e:
        print(f"❌ Error testing endpoints: {e}")
        return False
        
    return True

def test_sse_connection():
    """Test SSE connection (basic connectivity test)"""
    print("\n🔌 Testing SSE connection...")
    
    try:
        import sseclient  # pip install sseclient-py
        
        response = requests.get(f"{SERVER_URL}/events", stream=True)
        client = sseclient.SSEClient(response)
        
        print("✅ SSE connection established")
        print("📡 Waiting for events (will timeout after 10 seconds)...")
        
        event_count = 0
        start_time = time.time()
        
        for event in client.events():
            if time.time() - start_time > 10:  # 10 second timeout
                break
                
            if event.data and event.data != 'ping':
                event_count += 1
                try:
                    data = json.loads(event.data)
                    print(f"✅ Received event {event_count}: {event.event or 'message'}")
                    if 'brandColors' in data:
                        print(f"   Brand colors: {list(data['brandColors'].keys())}")
                except json.JSONDecodeError:
                    print(f"⚠️  Received non-JSON data: {event.data[:50]}...")
                    
        if event_count > 0:
            print(f"✅ SSE test completed. Received {event_count} events")
        else:
            print("⚠️  No events received (this might be normal if no updates occurred)")
            
    except ImportError:
        print("⚠️  sseclient-py not installed. Skipping detailed SSE test.")
        print("   Install with: pip install sseclient-py")
        
        # Basic connection test without sseclient
        try:
            response = requests.get(f"{SERVER_URL}/events", stream=True, timeout=5)
            if response.status_code == 200:
                print("✅ SSE endpoint is accessible")
            else:
                print(f"❌ SSE endpoint returned: {response.status_code}")
        except requests.exceptions.Timeout:
            print("✅ SSE endpoint is accessible (connection timeout is expected)")
        except Exception as e:
            print(f"❌ SSE connection failed: {e}")
            
    except Exception as e:
        print(f"❌ SSE test failed: {e}")

def test_manual_trigger():
    """Test manual update trigger"""
    print("\n🔄 Testing manual update trigger...")
    
    try:
        response = requests.post(f"{SERVER_URL}/trigger-update")
        if response.status_code == 200:
            print("✅ Manual trigger working")
            data = response.json()
            print(f"   Clients notified: {data.get('clients_notified', 0)}")
        else:
            print(f"❌ Manual trigger failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Manual trigger test failed: {e}")

def test_file_modification():
    """Test file modification detection"""
    print("\n📝 Testing file modification detection...")
    print("   This test will modify brand.json and restore it")
    
    try:
        # Read current brand.json
        with open('brand.json', 'r') as f:
            original_content = f.read()
            
        # Modify the file
        data = json.loads(original_content)
        data['_test_timestamp'] = int(time.time())
        
        with open('brand.json', 'w') as f:
            json.dump(data, f, indent=2)
            
        print("✅ Modified brand.json")
        print("   Check your SSE clients - they should receive an update")
        
        # Wait a moment
        time.sleep(2)
        
        # Restore original content
        with open('brand.json', 'w') as f:
            f.write(original_content)
            
        print("✅ Restored original brand.json")
        print("   Check your SSE clients - they should receive another update")
        
    except Exception as e:
        print(f"❌ File modification test failed: {e}")
        # Try to restore original content
        try:
            with open('brand.json', 'w') as f:
                f.write(original_content)
        except:
            pass

def main():
    """Run all tests"""
    print("🚀 Flask SSE Server Test Suite")
    print("=" * 50)
    
    # Test basic endpoints
    if not test_server_endpoints():
        print("\n❌ Basic endpoint tests failed. Server might not be running.")
        print("   Start the server with: python app.py")
        return
    
    # Test SSE connection
    test_sse_connection()
    
    # Test manual trigger
    test_manual_trigger()
    
    # Test file modification
    test_file_modification()
    
    print("\n" + "=" * 50)
    print("🎉 Test suite completed!")
    print("\nNext steps:")
    print("1. Keep the Flask server running")
    print("2. Open http://localhost:5001/events in your browser")
    print("3. Modify brand.json and watch for real-time updates")
    print("4. Integrate the client examples into your Next.js app")

if __name__ == "__main__":
    main()
